<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Webkul\Checkout\Facades\Cart;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\MLKWebAPI\Http\Resources\CartResource;
use Webkul\MLKWebAPI\Http\Resources\ProductResource;
use Webkul\Checkout\Repositories\CartItemRepository;
use Webkul\CartRule\Repositories\CartRuleCouponRepository;
use Webkul\Checkout\Models\CartAddress;
use Webkul\Shipping\Facades\Shipping;

class CartController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository,
        protected CartItemRepository $cartItemRepository,
        protected CartRuleCouponRepository $cartRuleCouponRepository
    ) {
    }

    /**
     * 获取购物车
     */
    public function index(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            Cart::collectTotals();

            return $this->success(
                ($cart = Cart::getCart()) ? new CartResource($cart) : null,
                trans('mlk::app.checkout.cart.success-index')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 添加商品到购物车
     */
    public function add(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'product_id' => 'required|integer|exists:products,id',
                'quantity'   => 'sometimes|numeric|min:1'
            ]);

            $product = $this->productRepository->findOrFail(request()->input('product_id'));
            
            if (!$product->status) {
                return $this->error(trans('mlk::app.checkout.cart.inactive-add'));
            }

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            // 添加到购物车 request()->all() 包含所有请求参数,可以添加 is_buy_now 参数
            $cart = Cart::addProduct($product, request()->all());

            return $this->success(
                new CartResource($cart),
                trans('mlk::app.checkout.cart.success-add')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量添加商品到购物车
     */
    public function addBatch(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'products' => 'required|array|min:1',
                'products.*.product_id' => 'required|integer|exists:products,id',
                'products.*.quantity' => 'sometimes|numeric|min:1',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $products = request()->input('products');
            $successCount = 0;
            $errors = [];

            foreach ($products as $index => $productData) {
                try {
                    $product = $this->productRepository->findOrFail($productData['product_id']);
                    
                    if (!$product->status) {
                        $errors[] = [
                            'index' => $index,
                            'product_id' => $productData['product_id'],
                            'message' => trans('mlk::app.checkout.cart.inactive-add')
                        ];
                        continue;
                    }

                    // 准备添加参数
                    $addData = array_merge($productData, [
                        'quantity' => $productData['quantity'] ?? 1
                    ]);

                    Cart::addProduct($product, $addData);
                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'product_id' => $productData['product_id'],
                        'message' => $e->getMessage()
                    ];
                }
            }

            Cart::collectTotals();

            $response = [
                'cart' => ($cart = Cart::getCart()) ? new CartResource($cart) : null,
                'success_count' => $successCount,
                'total_count' => count($products)
            ];

            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            $message = $successCount > 0 
                ? trans('mlk::app.checkout.cart.success-batch-add', ['count' => $successCount])
                : trans('mlk::app.checkout.cart.error-batch-add');

            return $this->success($response, $message);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 从购物车移除商品
     */
    public function remove(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'cart_item_id' => 'required|exists:cart_items,id',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cartItemId = request()->input('cart_item_id');
            
            // 安全检查：确保购物车商品属于当前用户
            $cart = Cart::getCart();
            if (!$cart || !$this->isCartItemBelongsToCustomer($cartItemId, $cart->id)) {
                return $this->unauthorized(trans('mlk::app.checkout.cart.unauthorized'));
            }
            
            Cart::removeItem($cartItemId);
            Cart::collectTotals();

            return $this->success(
                new CartResource(Cart::getCart()),
                trans('mlk::app.checkout.cart.success-remove')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新购物车商品数量
     */
    public function update(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'qty' => 'required|array',
                'qty.*' => 'numeric|min:1',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }
            
            // 安全检查：确保所有要更新的商品都属于当前用户的购物车
            foreach (array_keys(request()->input('qty')) as $itemId) {
                if (!$this->isCartItemBelongsToCustomer($itemId, $cart->id)) {
                    return $this->unauthorized(trans('mlk::app.checkout.cart.unauthorized'));
                }
            }
            
            Cart::updateItems(request()->all());

            return $this->success(
                new CartResource(Cart::getCart()),
                trans('mlk::app.checkout.cart.success-update')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清空购物车
     */
    public function clear(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            if ($cart = Cart::getCart()) {
                foreach ($cart->items as $item) {
                    Cart::removeItem($item->id);
                }
            }

            return $this->success(null, trans('mlk::app.checkout.cart.success-clear'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除选中的购物车商品
     */
    public function destroySelected(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:cart_items,id',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            // 安全检查：确保所有要删除的商品都属于当前用户的购物车
            foreach (request()->input('ids') as $itemId) {
                if (!$this->isCartItemBelongsToCustomer($itemId, $cart->id)) {
                    return $this->unauthorized(trans('mlk::app.checkout.cart.unauthorized'));
                }
                Cart::removeItem($itemId);
            }

            Cart::collectTotals();

            return $this->success(
                ($cart = Cart::getCart()) ? new CartResource($cart) : null,
                trans('mlk::app.checkout.cart.success-remove-selected')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 将选中的购物车商品移至愿望清单
     */
    public function moveToWishlist(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:cart_items,id',
                'qty' => 'required|array',
                'qty.*' => 'numeric|min:1',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            $ids = request()->input('ids');
            $quantities = request()->input('qty');

            // 安全检查：确保所有要移动的商品都属于当前用户的购物车
            foreach ($ids as $index => $itemId) {
                if (!$this->isCartItemBelongsToCustomer($itemId, $cart->id)) {
                    return $this->unauthorized(trans('mlk::app.checkout.cart.unauthorized'));
                }

                $qty = $quantities[$index] ?? 1;
                Cart::moveToWishlist($itemId, $qty);
            }

            Cart::collectTotals();

            return $this->success(
                ($cart = Cart::getCart()) ? new CartResource($cart) : null,
                trans('mlk::app.checkout.cart.success-move-to-wishlist')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 应用优惠券
     */
    public function applyCoupon(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'code' => 'required|string',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            $code = request()->input('code');

            if (strlen($code)) {
                $coupon = $this->cartRuleCouponRepository->findOneByField('code', $code);

                if (!$coupon) {
                    return $this->error(trans('mlk::app.checkout.cart.coupon-not-found'));
                }

                if ($coupon->cart_rule->status) {
                    if ($cart->coupon_code == $code) {
                        return $this->error(trans('mlk::app.checkout.cart.coupon-already-applied'));
                    }

                    Cart::setCouponCode($code)->collectTotals();

                    if (Cart::getCart()->coupon_code == $code) {
                        return $this->success(
                            new CartResource(Cart::getCart()),
                            trans('mlk::app.checkout.cart.coupon-success-apply')
                        );
                    }
                }

                return $this->error(trans('mlk::app.checkout.cart.coupon-not-found'));
            }

            return $this->error(trans('mlk::app.checkout.cart.coupon-invalid'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 移除优惠券
     */
    public function removeCoupon(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            Cart::removeCouponCode()->collectTotals();

            return $this->success(
                new CartResource(Cart::getCart()),
                trans('mlk::app.checkout.cart.coupon-remove')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 估算运费和税费
     */
    public function estimateShippingMethods(): JsonResponse
    {
        try {
            $this->validate(request(), [
                'country' => 'required|string',
                'state' => 'required|string',
                'postcode' => 'required|string',
                'shipping_method' => 'sometimes|required|string',
            ]);

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            $address = (new CartAddress)->fill([
                'country' => request()->input('country'),
                'state' => request()->input('state'),
                'postcode' => request()->input('postcode'),
                'cart_id' => $cart->id,
            ]);

            $cart->setRelation('billing_address', $address);
            $cart->setRelation('shipping_address', $address);

            Cart::setCart($cart);

            if (request()->has('shipping_method')) {
                Cart::saveShippingMethod(request()->input('shipping_method'));
            }

            Cart::collectTotals();

            $cartResource = (new CartResource(Cart::getCart()))->jsonSerialize();
            $shippingRates = Shipping::collectRates();

            return $this->success([
                'cart' => $cartResource,
                'shipping_methods' => array_values($shippingRates['shippingMethods'] ?? []),
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取交叉销售商品
     */
    public function crossSellProducts(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();

            // 确保Cart实例使用当前用户
            Cart::initCart($customer);

            $cart = Cart::getCart();

            if (!$cart) {
                return $this->success([]);
            }

            $productIds = $cart->items->pluck('product_id')->toArray();

            $products = $this->productRepository
                ->select('products.*', 'product_cross_sells.child_id')
                ->join('product_cross_sells', 'products.id', '=', 'product_cross_sells.child_id')
                ->whereIn('product_cross_sells.parent_id', $productIds)
                ->groupBy('product_cross_sells.child_id')
                ->take(core()->getConfigData('catalog.products.cart_view_page.no_of_cross_sells_products'))
                ->get();

            return $this->success(ProductResource::collection($products));
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查购物车商品是否属于指定的购物车
     *
     * @param int $cartItemId
     * @param int $cartId
     * @return bool
     */
    protected function isCartItemBelongsToCustomer($cartItemId, $cartId)
    {
        $cartItem = $this->cartItemRepository->findOneWhere([
            'id' => $cartItemId,
            'cart_id' => $cartId,
        ]);

        return $cartItem !== null;
    }
}
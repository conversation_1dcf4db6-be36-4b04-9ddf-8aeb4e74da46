{"__meta": {"id": "01K1YMAE13ZYM2SW2PRSCQ1YB0", "datetime": "2025-08-06 03:45:45", "utime": **********.123968, "method": "GET", "uri": "/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754448344.916934, "end": **********.134864, "duration": 0.21793007850646973, "duration_str": "218ms", "measures": [{"label": "Booting", "start": 1754448344.916934, "relative_start": 0, "end": **********.10131, "relative_end": **********.10131, "duration": 0.*****************, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.101322, "relative_start": 0.****************, "end": **********.134866, "relative_end": 1.9073486328125e-06, "duration": 0.033544063568115234, "duration_str": "33.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.113224, "relative_start": 0.****************, "end": **********.116843, "relative_end": **********.116843, "duration": 0.003618955612182617, "duration_str": "3.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.122125, "relative_start": 0.*****************, "end": **********.122228, "relative_end": **********.122228, "duration": 0.000102996826171875, "duration_str": "103μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.12224, "relative_start": 0.****************, "end": **********.122253, "relative_end": **********.122253, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "218ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-312093296 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-312093296\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1070362160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070362160\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-949011941 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Im9GbmN5K2t1Y2dub0F1U3ViNzdDS2c9PSIsInZhbHVlIjoiQ2NWekVINGM0RDF0RlltbW5HM2xCcUlFZnVwRUt0L0pReGxJenBLTGhORjJYeUtEaFpCblVMSlp6dzVsUGZLNVhUVnJHbm9EU0tzOWg1eDhGOXZVUHFGSE5NQXFkK3JtWUdOMHFLVWJyMU0zS0dKNW1FUFRwbXpsZlRGNlFOM2giLCJtYWMiOiJkNzk4YjA1NWRmNWNhODkyMTVlYjMyMTYzZGUzYThkZTY0NGM2M2M2YzgxZmRjNWQ4NTc1MmE5NmFmMjg5NDk4IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImRLRW1rRFdlbWtpYzNHcmlaYm1CVXc9PSIsInZhbHVlIjoiaTMrM1dIa3p6K0JEOE10aUdKZitvdm9BY1pLa2JvNFJlR1lQaGFXLzV1K1ZaaGF4c3RqVWdITHdhQWE4RElzTyt2QmJoby9KQWkrTm5ld3ozcmwzdEx0Tm9LM0lHbDJ5VjNhTUkzZ1lQZjJNSHJWMFRIV1dPQ054NmVyVGpUVEkiLCJtYWMiOiI1N2M4YmQyOTU5YzAyZGQ1M2I0NDVmMTY3ZjA1NjMyZGVhOTM4NjIzODlhMGQ1Mzc4ZjgzNWJiOTZkZGQzMWEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">http://mlk.test/search?brand=371&amp;sort=price-desc&amp;limit=2&amp;mode=grid</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949011941\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-64669925 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GbmN5K2t1Y2dub0F1U3ViNzdDS2c9PSIsInZhbHVlIjoiQ2NWekVINGM0RDF0RlltbW5HM2xCcUlFZnVwRUt0L0pReGxJenBLTGhORjJYeUtEaFpCblVMSlp6dzVsUGZLNVhUVnJHbm9EU0tzOWg1eDhGOXZVUHFGSE5NQXFkK3JtWUdOMHFLVWJyMU0zS0dKNW1FUFRwbXpsZlRGNlFOM2giLCJtYWMiOiJkNzk4YjA1NWRmNWNhODkyMTVlYjMyMTYzZGUzYThkZTY0NGM2M2M2YzgxZmRjNWQ4NTc1MmE5NmFmMjg5NDk4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImRLRW1rRFdlbWtpYzNHcmlaYm1CVXc9PSIsInZhbHVlIjoiaTMrM1dIa3p6K0JEOE10aUdKZitvdm9BY1pLa2JvNFJlR1lQaGFXLzV1K1ZaaGF4c3RqVWdITHdhQWE4RElzTyt2QmJoby9KQWkrTm5ld3ozcmwzdEx0Tm9LM0lHbDJ5VjNhTUkzZ1lQZjJNSHJWMFRIV1dPQ054NmVyVGpUVEkiLCJtYWMiOiI1N2M4YmQyOTU5YzAyZGQ1M2I0NDVmMTY3ZjA1NjMyZGVhOTM4NjIzODlhMGQ1Mzc4ZjgzNWJiOTZkZGQzMWEzIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64669925\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1570537678 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 02:45:45 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570537678\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-111339278 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111339278\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}
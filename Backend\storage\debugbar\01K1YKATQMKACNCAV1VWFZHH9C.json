{"__meta": {"id": "01K1YKATQMKACNCAV1VWFZHH9C", "datetime": "2025-08-06 03:28:29", "utime": **********.556847, "method": "GET", "uri": "/cache/original/product/178/PWAOnhlqNDdR2iLhUzOMp3SHmN4kvkViY0Sfatt5.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.347138, "end": **********.567001, "duration": 0.2198631763458252, "duration_str": "220ms", "measures": [{"label": "Booting", "start": **********.347138, "relative_start": 0, "end": **********.535738, "relative_end": **********.535738, "duration": 0.****************, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.535749, "relative_start": 0.*****************, "end": **********.567003, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "31.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.546328, "relative_start": 0.****************, "end": **********.550002, "relative_end": **********.550002, "duration": 0.003674030303955078, "duration_str": "3.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.555186, "relative_start": 0.*****************, "end": **********.555319, "relative_end": **********.555319, "duration": 0.00013303756713867188, "duration_str": "133μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.555335, "relative_start": 0.*****************, "end": **********.555348, "relative_end": **********.555348, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/178/PWAOnhlqNDdR2iLhUzOMp3SHmN4kvkViY0Sfatt5.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "220ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-115938933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115938933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-727813614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-727813614\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-143432293 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IklvY2F1UDBVSmFjNERDcVNzQjR0cFE9PSIsInZhbHVlIjoiM3RFNGNQemdFNVpEK1dZL2VIeXhZbEhmZTVQOUdwZjkrRUcreFhIMThmM0F4TXplcitlZnFacFJBcWZ1eGxWNldrU3E4U3FmeUpCMC9rb1ZuOW9rV0M4eU9lTElNcWxvSzlOSGJldnF2S3JUOXFieTVleHY2SUo0amdHajEwNWIiLCJtYWMiOiIyNjk1NWUzZDlmMTkwYTA0NDJmODU4NGFiZDFkOThhYzZmMDA5MjQ4NzEyMGI4NWJhOGY2M2UxMjNmOTEyOGFjIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InpZMUpnS1cyNlc0SmFRS3d2RzZLTGc9PSIsInZhbHVlIjoiZ2JKeHNLek1nM0NyMnJ1K2xmR0liTExkUjZQbnN4REttWWFYTzd5TGdQSjFJVTlwaTBWSXFFVjJHRWlrVllRWmdoWXFIZGtrK2FLaU5rNkQ0S0NzcytGNmFIWGRES3M3dUgrS3dKMlpoZ2FHbWtBNTFwOU1leGxaZy85ZEdvUTkiLCJtYWMiOiJhNTJiYTI2YzVkNGZjZGNiYzQxMWUzODE5NGZlMDJlM2EyMDdlZmJkOTg4NWM5MWQ2NzFkMDQ1NGVmNzY0MjU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://mlk.test/case-for-1031-easy-access-to-all-buttons</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143432293\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-401238104 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IklvY2F1UDBVSmFjNERDcVNzQjR0cFE9PSIsInZhbHVlIjoiM3RFNGNQemdFNVpEK1dZL2VIeXhZbEhmZTVQOUdwZjkrRUcreFhIMThmM0F4TXplcitlZnFacFJBcWZ1eGxWNldrU3E4U3FmeUpCMC9rb1ZuOW9rV0M4eU9lTElNcWxvSzlOSGJldnF2S3JUOXFieTVleHY2SUo0amdHajEwNWIiLCJtYWMiOiIyNjk1NWUzZDlmMTkwYTA0NDJmODU4NGFiZDFkOThhYzZmMDA5MjQ4NzEyMGI4NWJhOGY2M2UxMjNmOTEyOGFjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InpZMUpnS1cyNlc0SmFRS3d2RzZLTGc9PSIsInZhbHVlIjoiZ2JKeHNLek1nM0NyMnJ1K2xmR0liTExkUjZQbnN4REttWWFYTzd5TGdQSjFJVTlwaTBWSXFFVjJHRWlrVllRWmdoWXFIZGtrK2FLaU5rNkQ0S0NzcytGNmFIWGRES3M3dUgrS3dKMlpoZ2FHbWtBNTFwOU1leGxaZy85ZEdvUTkiLCJtYWMiOiJhNTJiYTI2YzVkNGZjZGNiYzQxMWUzODE5NGZlMDJlM2EyMDdlZmJkOTg4NWM5MWQ2NzFkMDQ1NGVmNzY0MjU3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401238104\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-815098137 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">51050</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">60f5af4014d20ce838dd3e4f022f2535</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 02:28:29 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815098137\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-954297092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954297092\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/178/PWAOnhlqNDdR2iLhUzOMp3SHmN4kvkViY0Sfatt5.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}
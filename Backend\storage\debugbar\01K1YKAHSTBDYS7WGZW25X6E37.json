{"__meta": {"id": "01K1YKAHSTBDYS7WGZW25X6E37", "datetime": "2025-08-06 03:28:20", "utime": **********.410758, "method": "GET", "uri": "/cache/medium/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.19694, "end": **********.42028, "duration": 0.22334003448486328, "duration_str": "223ms", "measures": [{"label": "Booting", "start": **********.19694, "relative_start": 0, "end": **********.388615, "relative_end": **********.388615, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.388625, "relative_start": 0.*****************, "end": **********.420282, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "31.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.399393, "relative_start": 0.****************, "end": **********.404206, "relative_end": **********.404206, "duration": 0.004812955856323242, "duration_str": "4.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.409307, "relative_start": 0.*****************, "end": **********.409412, "relative_end": **********.409412, "duration": 0.0001049041748046875, "duration_str": "105μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.409426, "relative_start": 0.*****************, "end": **********.409438, "relative_end": **********.409438, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "224ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-501497356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501497356\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1482095625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1482095625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2037636833 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImZYNVFUMzlobGsxS2FkanhrUm5VT3c9PSIsInZhbHVlIjoiK3pRbHBPK1FHVEZjMDYxWDZzek51OGV4S1pWUGFhWWxoTzFwWmpyN25VcjZWNjMweVJOYzF0eVBvQUpiVC9xNlhDMmFwQ1pVL3htcjIybjZVWHpaR0I2eWVGUHd3ZDBVTTkvREVFaEM0SnAxTjFtWVJHNjB5QW54RWIvNndTL2YiLCJtYWMiOiI3NWNlZmNlNzVhODkzZmY2NjE5NmQ0N2FkNjhhMzJhNTRjMWQ0NWQ1ZDhiMjkyODU3ZTc5ZjMwM2NlZDAxN2M3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjhGSFRqTUE2QU4vUmptcWZxd21SWmc9PSIsInZhbHVlIjoiSkFuU0pWM1JXZWpnaHZMaExFaHZ0a2dYNGlvV0VjZy8zRkhtNFVzanFyNCtHbVFWa3V2cVhyZmVvZ2h6QjNmUWw4UkxzblBueUJMSUdyb0Uxd3dCN2lPZmIzTFI5a3ZZMmFsU21nT1hpUjc4VnN5d1k4M0U2VDZ1MDJubnRjZDIiLCJtYWMiOiIzZDI0NjhjYzdiMWJjMGU4NWQ0ODkyZTlkMzEwMGU5ODlkNmEzZmIyZmMzZWQ0ZTA2MTk0ZjA3YzczOWI1MzU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">http://mlk.test/search?brand=371&amp;sort=price-desc&amp;limit=2&amp;mode=grid</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037636833\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-444003650 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImZYNVFUMzlobGsxS2FkanhrUm5VT3c9PSIsInZhbHVlIjoiK3pRbHBPK1FHVEZjMDYxWDZzek51OGV4S1pWUGFhWWxoTzFwWmpyN25VcjZWNjMweVJOYzF0eVBvQUpiVC9xNlhDMmFwQ1pVL3htcjIybjZVWHpaR0I2eWVGUHd3ZDBVTTkvREVFaEM0SnAxTjFtWVJHNjB5QW54RWIvNndTL2YiLCJtYWMiOiI3NWNlZmNlNzVhODkzZmY2NjE5NmQ0N2FkNjhhMzJhNTRjMWQ0NWQ1ZDhiMjkyODU3ZTc5ZjMwM2NlZDAxN2M3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjhGSFRqTUE2QU4vUmptcWZxd21SWmc9PSIsInZhbHVlIjoiSkFuU0pWM1JXZWpnaHZMaExFaHZ0a2dYNGlvV0VjZy8zRkhtNFVzanFyNCtHbVFWa3V2cVhyZmVvZ2h6QjNmUWw4UkxzblBueUJMSUdyb0Uxd3dCN2lPZmIzTFI5a3ZZMmFsU21nT1hpUjc4VnN5d1k4M0U2VDZ1MDJubnRjZDIiLCJtYWMiOiIzZDI0NjhjYzdiMWJjMGU4NWQ0ODkyZTlkMzEwMGU5ODlkNmEzZmIyZmMzZWQ0ZTA2MTk0ZjA3YzczOWI1MzU3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444003650\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-577299888 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5154</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">40e179829d23449ec2599e8761c62ea6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 02:28:20 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577299888\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1658092040 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1658092040\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}
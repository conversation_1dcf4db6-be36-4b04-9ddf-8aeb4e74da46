{"__meta": {"id": "01K1YTBDX5QZPYFR6HVYS32S9S", "datetime": "2025-08-06 05:31:09", "utime": **********.221989, "method": "POST", "uri": "/admin/configuration/customer/settings", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (22)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 40 limit 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.login_options.redirected_to_page'", "duration": 0.5, "duration_str": "500ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 41 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.create_new_account_options.default_group'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 42 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.create_new_account_options.news_letter'", "duration": 0.41, "duration_str": "410ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 43 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 44 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.email.verification'", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 45 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '1', `core_config`.`updated_at` = '2025-08-06 05:31:09' where `id` = 45", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_facebook' and `channel_code` = 'default'", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 17 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_twitter' and `channel_code` = 'default'", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 18 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_google' and `channel_code` = 'default'", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 19 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_linkedin-openid' and `channel_code` = 'default'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 46 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_github' and `channel_code` = 'default'", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 21 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.665164, "end": **********.23055, "duration": 0.5653860569000244, "duration_str": "565ms", "measures": [{"label": "Booting", "start": **********.665164, "relative_start": 0, "end": **********.839662, "relative_end": **********.839662, "duration": 0.****************, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.839676, "relative_start": 0.*****************, "end": **********.230552, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.851041, "relative_start": 0.*****************, "end": **********.852954, "relative_end": **********.852954, "duration": 0.001912832260131836, "duration_str": "1.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.219712, "relative_start": 0.****************, "end": **********.220003, "relative_end": **********.220003, "duration": 0.00029087066650390625, "duration_str": "291μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01184, "accumulated_duration_str": "11.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.866404, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 3.801}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.870681, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 3.801, "width_percent": 2.449}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.876348, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 6.25, "width_percent": 2.28}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.8781168, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 8.53, "width_percent": 1.943}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.879097, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 10.473, "width_percent": 1.267}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.882793, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 11.74, "width_percent": 1.689}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.885291, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 13.429, "width_percent": 1.436}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "type": "query", "params": [], "bindings": ["customer.settings.wishlist.wishlist_option"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.890191, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 14.865, "width_percent": 17.061}, {"sql": "select * from `core_config` where `core_config`.`id` = 40 limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.893471, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 31.926, "width_percent": 2.28}, {"sql": "select * from `core_config` where `code` = 'customer.settings.login_options.redirected_to_page'", "type": "query", "params": [], "bindings": ["customer.settings.login_options.redirected_to_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0262291, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 34.206, "width_percent": 4.223}, {"sql": "select * from `core_config` where `core_config`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0289779, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 38.429, "width_percent": 1.858}, {"sql": "select * from `core_config` where `code` = 'customer.settings.create_new_account_options.default_group'", "type": "query", "params": [], "bindings": ["customer.settings.create_new_account_options.default_group"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.046804, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 40.287, "width_percent": 3.547}, {"sql": "select * from `core_config` where `core_config`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.048155, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 43.834, "width_percent": 1.351}, {"sql": "select * from `core_config` where `code` = 'customer.settings.create_new_account_options.news_letter'", "type": "query", "params": [], "bindings": ["customer.settings.create_new_account_options.news_letter"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0651798, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 45.186, "width_percent": 3.463}, {"sql": "select * from `core_config` where `core_config`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.066517, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 48.649, "width_percent": 1.267}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "type": "query", "params": [], "bindings": ["customer.settings.newsletter.subscription"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.083379, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 49.916, "width_percent": 2.787}, {"sql": "select * from `core_config` where `core_config`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0845811, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 52.703, "width_percent": 1.351}, {"sql": "select * from `core_config` where `code` = 'customer.settings.email.verification'", "type": "query", "params": [], "bindings": ["customer.settings.email.verification"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.100952, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 54.054, "width_percent": 3.378}, {"sql": "select * from `core_config` where `core_config`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.102299, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 57.432, "width_percent": 1.605}, {"sql": "update `core_config` set `value` = '1', `core_config`.`updated_at` = '2025-08-06 05:31:09' where `id` = 45", "type": "query", "params": [], "bindings": ["1", "2025-08-06 05:31:09", 45], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1033869, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 59.037, "width_percent": 17.99}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_facebook' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["customer.settings.social_login.enable_facebook", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.123465, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 77.027, "width_percent": 2.956}, {"sql": "select * from `core_config` where `core_config`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.12468, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 79.983, "width_percent": 1.436}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_twitter' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["customer.settings.social_login.enable_twitter", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.141439, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 81.419, "width_percent": 2.534}, {"sql": "select * from `core_config` where `core_config`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.142597, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 83.953, "width_percent": 1.182}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_google' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["customer.settings.social_login.enable_google", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.158746, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 85.135, "width_percent": 2.872}, {"sql": "select * from `core_config` where `core_config`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.159886, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 88.007, "width_percent": 1.52}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_linkedin-openid' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["customer.settings.social_login.enable_linkedin-openid", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.177188, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 89.527, "width_percent": 3.547}, {"sql": "select * from `core_config` where `core_config`.`id` = 46 limit 1", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.178554, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 93.074, "width_percent": 1.52}, {"sql": "select * from `core_config` where `code` = 'customer.settings.social_login.enable_github' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["customer.settings.social_login.enable_github", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1966698, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 94.595, "width_percent": 3.632}, {"sql": "select * from `core_config` where `core_config`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.198174, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 98.226, "width_percent": 1.774}]}, "models": {"data": {"Webkul\\Core\\Models\\CoreConfig": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 25, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/customer/settings", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store", "uri": "POST admin/configuration/{slug?}/{slug2?}", "controller": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/configuration/{slug?}/{slug2?}", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php:53-128</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "568ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/configuration/customer/settings", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1899135699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1899135699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1158528337 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0v2eMPQj1P3qUT9pv7IA76tb9Zgj8rnR3dcjy9m5</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>keys</span>\" => <span class=sf-dump-note>array:26</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"366 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;wishlist_option&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.wishlist.allow-option&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;default&quot;:1}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u542f\\u7528\\u6216\\u7981\\u7528\\u613f\\u671b\\u6e05\\u5355\\u9009\\u9879\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.wishlist&quot;,&quot;name&quot;:&quot;\\u613f\\u671b\\u6e05\\u5355&quot;,&quot;route&quot;:null,&quot;sort&quot;:2}</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"649 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;redirected_to_page&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.login-options.redirect-to-page&quot;,&quot;type&quot;:&quot;select&quot;,&quot;default&quot;:&quot;home&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.login-options.home&quot;,&quot;value&quot;:&quot;home&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.login-options.account&quot;,&quot;value&quot;:&quot;account&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u8bbe\\u7f6e\\u767b\\u5f55\\u9009\\u9879\\uff0c\\u786e\\u5b9a\\u987e\\u5ba2\\u767b\\u5f55\\u540e\\u7684\\u91cd\\u5b9a\\u5411\\u9875\\u9762\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.login_options&quot;,&quot;name&quot;:&quot;\\u767b\\u5f55\\u9009\\u9879&quot;,&quot;route&quot;:null,&quot;sort&quot;:3}</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"1304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;default_group&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.title-info&quot;,&quot;type&quot;:&quot;select&quot;,&quot;default&quot;:&quot;guest&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.general&quot;,&quot;value&quot;:&quot;general&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.guest&quot;,&quot;value&quot;:&quot;guest&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.wholesale&quot;,&quot;value&quot;:&quot;wholesale&quot;}]},{&quot;name&quot;:&quot;news_letter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.news-letter&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.news-letter-info&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;default&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u8bbe\\u7f6e\\u65b0\\u8d26\\u6237\\u7684\\u9009\\u9879\\uff0c\\u5305\\u62ec\\u9ed8\\u8ba4\\u987e\\u5ba2\\u7ec4\\u7684\\u5206\\u914d\\u548c\\u6ce8\\u518c\\u65f6\\u7684\\u65b0\\u95fb\\u901a\\u8baf\\u8ba2\\u9605\\u9009\\u9879\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.create_new_account_options&quot;,&quot;name&quot;:&quot;\\u65b0\\u5efa\\u8d26\\u6237\\u9009\\u9879&quot;,&quot;route&quot;:null,&quot;sort&quot;:4}</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"1304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;default_group&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.title-info&quot;,&quot;type&quot;:&quot;select&quot;,&quot;default&quot;:&quot;guest&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.general&quot;,&quot;value&quot;:&quot;general&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.guest&quot;,&quot;value&quot;:&quot;guest&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.default-group.wholesale&quot;,&quot;value&quot;:&quot;wholesale&quot;}]},{&quot;name&quot;:&quot;news_letter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.news-letter&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.create-new-account-option.news-letter-info&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;default&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u8bbe\\u7f6e\\u65b0\\u8d26\\u6237\\u7684\\u9009\\u9879\\uff0c\\u5305\\u62ec\\u9ed8\\u8ba4\\u987e\\u5ba2\\u7ec4\\u7684\\u5206\\u914d\\u548c\\u6ce8\\u518c\\u65f6\\u7684\\u65b0\\u95fb\\u901a\\u8baf\\u8ba2\\u9605\\u9009\\u9879\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.create_new_account_options&quot;,&quot;name&quot;:&quot;\\u65b0\\u5efa\\u8d26\\u6237\\u9009\\u9879&quot;,&quot;route&quot;:null,&quot;sort&quot;:4}</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"622 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;subscription&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.newsletter.subscription&quot;,&quot;info&quot;:&quot;Enable subscription option for users in the footer section.&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;default&quot;:1}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u201c\\u901a\\u8baf\\u4fe1\\u606f\\u201d\\u662f\\u901a\\u8fc7\\u7535\\u5b50\\u90ae\\u4ef6\\u5b9a\\u671f\\u5411\\u8ba2\\u9605\\u8005\\u53d1\\u9001\\u7684\\u66f4\\u65b0\\u3001\\u4f18\\u60e0\\u6216\\u5185\\u5bb9\\uff0c\\u5e2e\\u52a9\\u4ed6\\u4eec\\u4fdd\\u6301\\u4e86\\u89e3\\u548c\\u53c2\\u4e0e\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.newsletter&quot;,&quot;name&quot;:&quot;\\u901a\\u8baf\\u4fe1\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:5}</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"555 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;verification&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.email.email-verification&quot;,&quot;type&quot;:&quot;boolean&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u201c\\u7535\\u5b50\\u90ae\\u4ef6\\u9a8c\\u8bc1\\u201d\\u901a\\u8fc7\\u53d1\\u9001\\u786e\\u8ba4\\u94fe\\u63a5\\u6765\\u9a8c\\u8bc1\\u7535\\u5b50\\u90ae\\u4ef6\\u5730\\u5740\\u7684\\u6709\\u6548\\u6027\\uff0c\\u589e\\u5f3a\\u8d26\\u6237\\u5b89\\u5168\\u6027\\u548c\\u901a\\u4fe1\\u53ef\\u9760\\u6027\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.email&quot;,&quot;name&quot;:&quot;\\u7535\\u5b50\\u90ae\\u4ef6\\u9a8c\\u8bc1&quot;,&quot;route&quot;:null,&quot;sort&quot;:6}</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"5289 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enable_facebook&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.enable-facebook&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;facebook_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;facebook_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.facebook.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_facebook:1&quot;},{&quot;name&quot;:&quot;enable_twitter&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.enable-twitter&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;twitter_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;twitter_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.twitter.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_twitter:1&quot;},{&quot;name&quot;:&quot;enable_google&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.enable-google&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;google_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;google_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.google.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_google:1&quot;},{&quot;name&quot;:&quot;enable_linkedin-openid&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.enable-linkedin&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;linkedin_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;linkedin_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.linkedin.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_linkedin-openid:1&quot;},{&quot;name&quot;:&quot;enable_github&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.enable-github&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;github_client_id&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-id.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_client_secret&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.client-secret.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;},{&quot;name&quot;:&quot;github_callback_url&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title&quot;,&quot;info&quot;:&quot;admin::app.configuration.index.customer.settings.social-login.github.redirect.title-info&quot;,&quot;type&quot;:&quot;text&quot;,&quot;depends&quot;:&quot;enable_github:1&quot;}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\&quot;\\u793e\\u4ea4\\u767b\\u5f55\\&quot;\\u5141\\u8bb8\\u7528\\u6237\\u901a\\u8fc7\\u4ed6\\u4eec\\u7684\\u793e\\u4ea4\\u5a92\\u4f53\\u8d26\\u6237\\u8bbf\\u95ee\\u7f51\\u7ad9\\uff0c\\u4ece\\u800c\\u7b80\\u5316\\u6ce8\\u518c\\u548c\\u767b\\u5f55\\u8fc7\\u7a0b\\u3002&quot;,&quot;key&quot;:&quot;customer.settings.social_login&quot;,&quot;name&quot;:&quot;\\u793e\\u4ea4\\u767b\\u5f55&quot;,&quot;route&quot;:null,&quot;sort&quot;:7}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>customer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>wishlist_option</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>login_options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>redirected_to_page</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>create_new_account_options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>default_group</span>\" => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n        \"<span class=sf-dump-key>news_letter</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>newsletter</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>subscription</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>verification</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>social_login</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enable_facebook</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>enable_twitter</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>enable_google</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>enable_linkedin-openid</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>enable_github</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158528337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-659396971 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImNzRjBmQksvNHFWOVBVQ0wvZmVpVlE9PSIsInZhbHVlIjoiTzd0NEppaXp2a2Yvckdob0t1b3g4M28zYlg0MEF6UytUR2VZRGZDaUxwVzZiSmtVcy9vKytnTzRUS05jZnZlZ25sUkFmdk9kYm1WeEpCTFIwYUN3amlGRFQ1d0ZJOWpma0dUVENadFV1WW1Fb1hITlB3TUdhelp2MG91L0l6RTgiLCJtYWMiOiIzYTRiNjY4NTJjZTI0OWQzNjU2YmZjOThlZjVjMjExMjAzZmY2ZGQ2OTFmODFkNWJiOTAwMzc4OWU3YWNkZmI3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ik16MjdoSVh1RjJsSDhQT241WlhXRlE9PSIsInZhbHVlIjoiTzV3VktyNzVja0d3Q2cvaXQ4SHpoUHlES21TOXRFeGhlTDh4c1V3T3dFY0M4akpZSlMxZVZnUGpYMVJIUjVOeWQxRzYwNk42dVVSOGRVckNmd21mYkFLQUoyU0N5aFUyRTdSRDR6RUFETDlhUFRmRUVLU2ZCbndaYWg2a082THQiLCJtYWMiOiIwZDIzMDlhZjNjNDQ0OWUxOWQ0OGI0OWMxYjdkYTgxZDI4N2FmMDRhNWI3YmUzMTYyZmVkNTljNzgyZjI5YjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://mlk.test/admin/configuration/customer/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryY39ImNCCN83zkfHz</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">115433</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659396971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1093593586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0v2eMPQj1P3qUT9pv7IA76tb9Zgj8rnR3dcjy9m5</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2q9c0JNByJrTw1bpWFWWus5Hjv309qWRCnL3TP8r</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093593586\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1677301891 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 04:31:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://mlk.test/admin/configuration/customer/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677301891\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-887163246 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0v2eMPQj1P3qUT9pv7IA76tb9Zgj8rnR3dcjy9m5</span>\"\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://mlk.test/admin/configuration/customer/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#37197;&#32622;&#20445;&#23384;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887163246\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/customer/settings", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store"}, "badge": "302 Found"}}
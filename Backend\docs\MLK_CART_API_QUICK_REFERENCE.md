# MLK购物车API快速参考

## 基础信息
- **基础URL**: `/api/mlk`
- **认证**: `Authorization: Bearer {token}`
- **Content-Type**: `application/json`

## 购物车基础操作

| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/cart` | 获取购物车 |
| POST | `/cart/add` | 添加商品 |
| POST | `/cart/add-batch` | 批量添加商品 |
| POST | `/cart/update` | 更新数量 |
| POST | `/cart/remove` | 移除商品 |
| POST | `/cart/clear` | 清空购物车 |

## 购物车高级功能

| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/cart/remove-selected` | 批量删除 |
| POST | `/cart/move-to-wishlist` | 移至愿望清单 |
| POST | `/cart/coupon/apply` | 应用优惠券 |
| POST | `/cart/coupon/remove` | 移除优惠券 |
| POST | `/cart/estimate-shipping` | 估算运费 |
| GET | `/cart/cross-sell` | 交叉销售商品 |

## 结账流程

| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/checkout/summary` | 结账摘要 |
| POST | `/checkout/addresses` | 保存地址 |
| POST | `/checkout/shipping-methods` | 选择配送方式 |
| POST | `/checkout/payment-methods` | 选择支付方式 |
| POST | `/checkout/orders` | 提交订单 |

## 快速示例

### 添加商品到购物车
```bash
POST /api/mlk/cart/add
{
    "product_id": 456,
    "quantity": 2,
    "super_attribute": {
        "23": 1,  // 颜色：红色
        "25": 371 // 品牌：Apple
    }
}
```

### 批量添加商品到购物车
```bash
POST /api/mlk/cart/add-batch
{
    "products": [
        {
            "product_id": 456,
            "quantity": 2,
            "super_attribute": {
                "23": 1,  // 颜色：红色
                "25": 371 // 品牌：Apple
            }
        },
        {
            "product_id": 789,
            "quantity": 1
        }
    ]
}
```

### 应用优惠券
```bash
POST /api/mlk/cart/coupon/apply
{
    "code": "DISCOUNT10"
}
```

### 估算运费
```bash
POST /api/mlk/cart/estimate-shipping
{
    "country": "US",
    "state": "CA",
    "postcode": "90210"
}
```

### 完整结账流程
```bash
# 1. 获取摘要
GET /api/mlk/checkout/summary

# 2. 保存地址
POST /api/mlk/checkout/addresses
{
    "billing": {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "address1": "123 Main St",
        "country": "US",
        "state": "CA",
        "city": "Los Angeles",
        "postcode": "90210",
        "phone": "555-1234"
    },
    "use_for_shipping": true
}

# 3. 选择配送
POST /api/mlk/checkout/shipping-methods
{
    "shipping_method": "flatrate_flatrate"
}

# 4. 选择支付
POST /api/mlk/checkout/payment-methods
{
    "payment": {
        "method": "cashondelivery"
    }
}

# 5. 提交订单
POST /api/mlk/checkout/orders
```

## 常用属性ID参考

### 商品属性
- **颜色 (ID: 23)**:
  - 红色: 1
  - 绿色: 2
  - 黄色: 3
  - 黑色: 4
  - 白色: 5

- **品牌 (ID: 25)**:
  - Apple: 371
  - Samsung: 372
  - Huawei: 373

- **设备型号 (ID: 33)**:
  - iPhone 16 Pro Max: 381

## 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息"
}
```

## 常见错误码

| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器错误 |

## 注意事项

1. 所有接口都需要Bearer Token认证
2. 购物车操作会实时更新总价
3. 结账流程需要按顺序执行
4. 优惠券有使用条件限制
5. 运费计算基于商品重量和地址

---

📖 **详细文档**: 查看 `MLK_CART_API_DOCUMENTATION.md` 获取完整接口说明

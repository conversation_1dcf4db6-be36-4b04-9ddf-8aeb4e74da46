# MLK购物车API接口文档

## 概述

本文档详细说明了MLKWebAPI包中购物车相关的所有API接口，包括基础购物车操作、高级功能和完整的结账流程。

## 基础信息

- **基础URL**: `/api/mlk`
- **认证方式**: <PERSON><PERSON> (Sanctum)
- **Content-Type**: `application/json`
- **响应格式**: JSON

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "data": {}, // 响应数据
    "message": "操作成功消息"
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误消息",
    "errors": {} // 验证错误详情（可选）
}
```

## 购物车基础功能

### 1. 获取购物车内容

**接口**: `GET /api/mlk/cart`

**描述**: 获取当前用户的购物车详细信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "customer_id": 123,
        "is_guest": false,
        "items_count": 2,
        "items_qty": 3,
        "sub_total": 299.99,
        "grand_total": 329.99,
        "items": [
            {
                "id": 1,
                "product_id": 456,
                "quantity": 2,
                "price": 149.99,
                "total": 299.98,
                "product": {
                    "id": 456,
                    "name": "iPhone 16 Pro Max",
                    "sku": "IPHONE-16-PRO-MAX",
                    "images": [...]
                }
            }
        ]
    },
    "message": "Cart retrieved successfully"
}
```

### 2. 添加商品到购物车

**接口**: `POST /api/mlk/cart/add`

**描述**: 将商品添加到购物车

**请求参数**:
```json
{
    "product_id": 456,
    "quantity": 2,
    "super_attribute": {
        "23": 1,  // 颜色属性：红色
        "25": 371 // 品牌属性：Apple
    }
}
```

**参数说明**:
- `product_id` (必填): 商品ID
- `quantity` (可选): 数量，默认为1
- `super_attribute` (可选): 可配置商品的属性选择
- `is_buy_now` (可选): 是否立即购买

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 3,
        "grand_total": 449.98
    },
    "message": "Item added to cart successfully"
}
```

### 3. 批量添加商品到购物车

**接口**: `POST /api/mlk/cart/add-batch`

**描述**: 一次性批量添加多个商品到购物车

**请求参数**:
```json
{
    "products": [
        {
            "product_id": 456,
            "quantity": 2,
            "super_attribute": {
                "23": 1,  // 颜色属性：红色
                "25": 371 // 品牌属性：Apple
            }
        },
        {
            "product_id": 789,
            "quantity": 1,
            "super_attribute": {
                "23": 2,  // 颜色属性：蓝色
                "25": 372 // 品牌属性：Samsung
            }
        },
        {
            "product_id": 101,
            "quantity": 3
        }
    ]
}
```

**参数说明**:
- `products` (必填): 商品数组
  - `product_id` (必填): 商品ID
  - `quantity` (可选): 数量，默认为1
  - `super_attribute` (可选): 可配置商品的属性选择
  - 其他参数与单个添加接口相同

**响应示例**:
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "items_count": 6,
            "grand_total": 899.97,
            "items": [...]
        },
        "success_count": 3,
        "total_count": 3,
        "errors": []
    },
    "message": "3 items added to cart successfully"
}
```

**部分成功响应示例**:
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "items_count": 4,
            "grand_total": 649.98
        },
        "success_count": 2,
        "total_count": 3,
        "errors": [
            {
                "index": 1,
                "product_id": 789,
                "message": "Inactive item cannot be added to cart."
            }
        ]
    },
    "message": "2 items added to cart successfully"
}
```

**响应数据说明**:
- `cart`: 更新后的购物车信息
- `success_count`: 成功添加的商品数量
- `total_count`: 请求添加的总商品数量
- `errors`: 失败商品的错误详情（如果有）

**特点**:
- 支持批量添加多个不同商品
- 部分成功处理：即使某些商品添加失败，成功的商品仍会被添加
- 详细错误报告：返回每个失败商品的具体错误信息
- 安全验证：验证商品存在性、库存状态等
- 性能优化：一次请求完成多个商品添加，减少网络请求

**使用场景**:
- 从愿望清单批量添加到购物车
- 推荐商品一键批量加购
- 套装商品批量添加
- 历史订单重新购买

### 4. 更新购物车商品数量

**接口**: `POST /api/mlk/cart/update`

**描述**: 更新购物车中商品的数量

**请求参数**:
```json
{
    "qty": {
        "1": 3,  // 购物车商品ID: 新数量
        "2": 1
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 2,
        "grand_total": 549.97
    },
    "message": "Cart updated successfully"
}
```

### 4. 移除购物车商品

**接口**: `POST /api/mlk/cart/remove`

**描述**: 从购物车中移除指定商品

**请求参数**:
```json
{
    "cart_item_id": 1
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 1,
        "grand_total": 149.99
    },
    "message": "Item removed from cart successfully"
}
```

### 5. 清空购物车

**接口**: `POST /api/mlk/cart/clear`

**描述**: 清空当前用户购物车中的所有商品

**响应示例**:
```json
{
    "success": true,
    "data": null,
    "message": "Cart cleared successfully"
}
```

## 购物车高级功能

### 6. 批量删除选中商品

**接口**: `POST /api/mlk/cart/remove-selected`

**描述**: 批量删除购物车中选中的商品

**请求参数**:
```json
{
    "ids": [1, 2, 3]  // 购物车商品ID数组
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 0,
        "grand_total": 0
    },
    "message": "Selected items successfully removed from cart"
}
```

### 7. 移至愿望清单

**接口**: `POST /api/mlk/cart/move-to-wishlist`

**描述**: 将购物车中的商品移动到愿望清单

**请求参数**:
```json
{
    "ids": [1, 2],     // 购物车商品ID数组
    "qty": [1, 2]      // 对应的数量数组
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 0,
        "grand_total": 0
    },
    "message": "Selected items successfully moved to wishlist"
}
```

### 8. 应用优惠券

**接口**: `POST /api/mlk/cart/coupon/apply`

**描述**: 为购物车应用优惠券代码

**请求参数**:
```json
{
    "code": "DISCOUNT10"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "coupon_code": "DISCOUNT10",
        "discount_amount": 29.99,
        "grand_total": 299.99
    },
    "message": "Coupon code applied successfully"
}
```

### 9. 移除优惠券

**接口**: `POST /api/mlk/cart/coupon/remove`

**描述**: 移除购物车中已应用的优惠券

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "coupon_code": null,
        "discount_amount": 0,
        "grand_total": 329.99
    },
    "message": "Coupon code removed successfully"
}
```

### 10. 估算运费

**接口**: `POST /api/mlk/cart/estimate-shipping`

**描述**: 根据地址信息估算运费和税费

**请求参数**:
```json
{
    "country": "US",
    "state": "CA",
    "postcode": "90210",
    "shipping_method": "flatrate_flatrate"  // 可选
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "shipping_amount": 15.00,
            "tax_amount": 24.00,
            "grand_total": 368.99
        },
        "shipping_methods": [
            {
                "method": "flatrate_flatrate",
                "method_title": "Flat Rate",
                "method_description": "Fixed shipping rate",
                "price": 15.00,
                "formatted_price": "$15.00"
            }
        ]
    }
}
```

### 11. 获取交叉销售商品

**接口**: `GET /api/mlk/cart/cross-sell`

**描述**: 获取基于购物车商品的推荐商品（交叉销售）

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 789,
            "name": "iPhone 16 Pro Max Case",
            "sku": "CASE-IPHONE-16",
            "price": 29.99,
            "formatted_price": "$29.99",
            "images": [...]
        }
    ]
}
```

## 结账流程

### 12. 获取结账摘要

**接口**: `GET /api/mlk/checkout/summary`

**描述**: 获取结账页面的购物车摘要信息

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "items_count": 2,
        "items_qty": 3,
        "sub_total": 299.99,
        "shipping_amount": 15.00,
        "tax_amount": 24.00,
        "grand_total": 338.99,
        "items": [...],
        "billing_address": null,
        "shipping_address": null
    }
}
```

### 13. 保存地址信息

**接口**: `POST /api/mlk/checkout/addresses`

**描述**: 保存结账时的账单地址和配送地址

**请求参数**:
```json
{
    "billing": {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "address1": "123 Main Street",
        "address2": "Apt 4B",
        "country": "US",
        "state": "CA",
        "city": "Los Angeles",
        "postcode": "90210",
        "phone": "555-1234",
        "company_name": "Acme Corp"
    },
    "shipping": {
        // 配送地址信息，格式同billing
    },
    "use_for_shipping": true  // 是否使用账单地址作为配送地址
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "redirect": false,
        "data": [
            {
                "method": "flatrate_flatrate",
                "method_title": "Flat Rate",
                "method_description": "Fixed shipping rate",
                "price": 15.00,
                "formatted_price": "$15.00"
            },
            {
                "method": "freeshipping_freeshipping",
                "method_title": "Free Shipping",
                "method_description": "Free shipping for orders over $100",
                "price": 0.00,
                "formatted_price": "Free"
            }
        ]
    }
}
```

### 14. 保存配送方式

**接口**: `POST /api/mlk/checkout/shipping-methods`

**描述**: 选择并保存配送方式

**请求参数**:
```json
{
    "shipping_method": "flatrate_flatrate"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "method": "cashondelivery",
            "method_title": "Cash On Delivery",
            "description": "Pay when you receive the order",
            "sort": 1
        },
        {
            "method": "moneytransfer",
            "method_title": "Money Transfer",
            "description": "Bank transfer payment",
            "sort": 2
        }
    ]
}
```

### 15. 保存支付方式

**接口**: `POST /api/mlk/checkout/payment-methods`

**描述**: 选择并保存支付方式

**请求参数**:
```json
{
    "payment": {
        "method": "cashondelivery",
        "additional_data": {
            "notes": "Please call before delivery"
        }
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "payment_method": "cashondelivery",
            "payment_method_title": "Cash On Delivery",
            "grand_total": 338.99
        }
    }
}
```

### 16. 提交订单

**接口**: `POST /api/mlk/checkout/orders`

**描述**: 创建订单并完成购买流程

**响应示例**:
```json
{
    "success": true,
    "data": {
        "order": {
            "id": 1001,
            "increment_id": "*********",
            "status": "pending",
            "customer_email": "<EMAIL>",
            "grand_total": 338.99,
            "created_at": "2024-01-15T10:30:00Z"
        },
        "message": "Order placed successfully"
    }
}
```

## 错误代码说明

| HTTP状态码 | 错误类型 | 说明 |
|-----------|---------|------|
| 400 | Bad Request | 请求参数错误或格式不正确 |
| 401 | Unauthorized | 未提供认证令牌或令牌无效 |
| 403 | Forbidden | 权限不足，无法访问资源 |
| 404 | Not Found | 请求的资源不存在 |
| 422 | Unprocessable Entity | 数据验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 常见错误示例

### 验证错误
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "product_id": ["The product id field is required."],
        "quantity": ["The quantity must be at least 1."]
    }
}
```

### 业务逻辑错误
```json
{
    "success": false,
    "message": "The requested quantity is not available, please try again later."
}
```

### 权限错误
```json
{
    "success": false,
    "message": "You are not authorized to access this cart."
}
```

## 使用示例

### 完整购物流程示例

```bash
# 1. 添加商品到购物车
curl -X POST "https://api.example.com/api/mlk/cart/add" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 456,
    "quantity": 2,
    "super_attribute": {
      "23": 1,
      "25": 371
    }
  }'

# 2. 应用优惠券
curl -X POST "https://api.example.com/api/mlk/cart/coupon/apply" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"code": "DISCOUNT10"}'

# 3. 估算运费
curl -X POST "https://api.example.com/api/mlk/cart/estimate-shipping" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "country": "US",
    "state": "CA",
    "postcode": "90210"
  }'

# 4. 开始结账流程
curl -X GET "https://api.example.com/api/mlk/checkout/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 5. 保存地址
curl -X POST "https://api.example.com/api/mlk/checkout/addresses" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "billing": {
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "address1": "123 Main St",
      "country": "US",
      "state": "CA",
      "city": "Los Angeles",
      "postcode": "90210",
      "phone": "555-1234"
    },
    "use_for_shipping": true
  }'

# 6. 选择配送方式
curl -X POST "https://api.example.com/api/mlk/checkout/shipping-methods" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"shipping_method": "flatrate_flatrate"}'

# 7. 选择支付方式
curl -X POST "https://api.example.com/api/mlk/checkout/payment-methods" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment": {
      "method": "cashondelivery"
    }
  }'

# 8. 提交订单
curl -X POST "https://api.example.com/api/mlk/checkout/orders" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. **认证要求**: 所有接口都需要有效的Bearer Token认证
2. **数据验证**: 请确保请求参数符合接口要求
3. **错误处理**: 客户端应妥善处理各种错误情况
4. **幂等性**: 某些操作（如添加商品）可能不是幂等的，重复调用会产生不同结果
5. **并发控制**: 在高并发场景下，建议实现适当的重试机制
6. **缓存策略**: 购物车数据会实时更新，建议避免过度缓存

## 更新日志

- **v1.0.0** (2024-01-15): 初始版本，包含所有购物车和结账功能
- 新增批量操作功能
- 新增优惠券功能
- 新增运费估算功能
- 新增完整结账流程
- 增强安全验证机制

{"__meta": {"id": "01K1YMCC72YQDCDGC4P1CFY2HX", "datetime": "2025-08-06 03:46:48", "utime": **********.802626, "method": "GET", "uri": "/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.589877, "end": **********.812917, "duration": 0.22304010391235352, "duration_str": "223ms", "measures": [{"label": "Booting", "start": **********.589877, "relative_start": 0, "end": **********.780264, "relative_end": **********.780264, "duration": 0.*****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.780276, "relative_start": 0.***************, "end": **********.812919, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "32.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.792229, "relative_start": 0.*****************, "end": **********.795874, "relative_end": **********.795874, "duration": 0.003645181655883789, "duration_str": "3.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.800979, "relative_start": 0.*****************, "end": **********.8011, "relative_end": **********.8011, "duration": 0.00012111663818359375, "duration_str": "121μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.801115, "relative_start": 0.*****************, "end": **********.801129, "relative_end": **********.801129, "duration": 1.4066696166992188e-05, "duration_str": "14μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "224ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-322038098 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-322038098\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-744713714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-744713714\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-815204960 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6InZmWHFyQmFLVEYzekxWMXdFbFNkSHc9PSIsInZhbHVlIjoiNVM2V2NyZStaQW5TcHFEV0RNMEVieGhYRlp2cmpueUF6cmFxZDVFM2RKdmxHbDY0YVBFY1llMWN0L3dQQmtRYlZwT0QvUFFuRmJ4ZXR4RnRuUGVtOUdTL21adDQwL21mRzU0VE1LV1ZhaUgzY1lnOThCeWoyS1UrK2kvSGxnN2IiLCJtYWMiOiJjZWU4OWZiYTk0ZTk5Yzc3YmNjN2Y3MTA2OTk1OTZlOTY3MDBhMzQ1ZDFiNTdkNDI3N2E0YTM5M2M5OGM0ZTE4IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InpwSVNyZ1pmOVNlTGZDS1Z4WXhWcmc9PSIsInZhbHVlIjoiS3pIUHdQUGgvekh5d1RncFptVUQ4LzZzaU12djFSeUxodEdvSTR0K2FhQ3NiSEcrWVdRK3I1eDVZWXF6c1ZZY2pFWU9qUnJCaGhsc0M3R2krYm1ZdkdXb2ZSOGJUbE90cGZISGNaaUFJMXB0RXp4b0s0T3RUQmlQTzBEZkNHWHIiLCJtYWMiOiIxNjZiMzhlODE1MjgyYzg0MmIxYjU1YTE3NDhmODM0ZjU5ZjA0ODI2YWIwNzA3NTAyMmFkOGViMmM3N2RlNzg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/search?device=385%2C383&amp;sort=price-desc&amp;limit=2&amp;mode=grid</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815204960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829523878 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZmWHFyQmFLVEYzekxWMXdFbFNkSHc9PSIsInZhbHVlIjoiNVM2V2NyZStaQW5TcHFEV0RNMEVieGhYRlp2cmpueUF6cmFxZDVFM2RKdmxHbDY0YVBFY1llMWN0L3dQQmtRYlZwT0QvUFFuRmJ4ZXR4RnRuUGVtOUdTL21adDQwL21mRzU0VE1LV1ZhaUgzY1lnOThCeWoyS1UrK2kvSGxnN2IiLCJtYWMiOiJjZWU4OWZiYTk0ZTk5Yzc3YmNjN2Y3MTA2OTk1OTZlOTY3MDBhMzQ1ZDFiNTdkNDI3N2E0YTM5M2M5OGM0ZTE4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InpwSVNyZ1pmOVNlTGZDS1Z4WXhWcmc9PSIsInZhbHVlIjoiS3pIUHdQUGgvekh5d1RncFptVUQ4LzZzaU12djFSeUxodEdvSTR0K2FhQ3NiSEcrWVdRK3I1eDVZWXF6c1ZZY2pFWU9qUnJCaGhsc0M3R2krYm1ZdkdXb2ZSOGJUbE90cGZISGNaaUFJMXB0RXp4b0s0T3RUQmlQTzBEZkNHWHIiLCJtYWMiOiIxNjZiMzhlODE1MjgyYzg0MmIxYjU1YTE3NDhmODM0ZjU5ZjA0ODI2YWIwNzA3NTAyMmFkOGViMmM3N2RlNzg2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829523878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-559564197 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">9430</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">44f0ecf702bf567366d9c4e958581d30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 02:46:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559564197\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1348947445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1348947445\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}
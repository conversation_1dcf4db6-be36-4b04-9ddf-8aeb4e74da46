{"__meta": {"id": "01K1Z8QM0G1WSGW1Q75DBDTS0V", "datetime": "2025-08-06 09:42:28", "utime": **********.752767, "method": "POST", "uri": "/api/mlk/customer/login?locale=en", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.6, "duration_str": "600ms", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\Customer (1)", "Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customers` where `email` = '<EMAIL>' limit 1", "duration": 2.68, "duration_str": "2.68s", "connection": "mlk"}, {"sql": "select * from `customer_groups` where `customer_groups`.`id` = 3 limit 1", "duration": 2.34, "duration_str": "2.34s", "connection": "mlk"}, {"sql": "select * from `customer_profiles` where `customer_profiles`.`customer_id` = 3 and `customer_profiles`.`customer_id` is not null limit 1", "duration": 2.82, "duration_str": "2.82s", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.192085, "end": **********.759302, "duration": 0.5672168731689453, "duration_str": "567ms", "measures": [{"label": "Booting", "start": **********.192085, "relative_start": 0, "end": **********.47538, "relative_end": **********.47538, "duration": 0.****************, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.475391, "relative_start": 0.****************, "end": **********.759303, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.490981, "relative_start": 0.*****************, "end": **********.497358, "relative_end": **********.497358, "duration": 0.006376981735229492, "duration_str": "6.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.751017, "relative_start": 0.****************, "end": **********.751334, "relative_end": **********.751334, "duration": 0.00031685829162597656, "duration_str": "317μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.751354, "relative_start": 0.****************, "end": **********.751366, "relative_end": **********.751366, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01479, "accumulated_duration_str": "14.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}], "start": **********.513475, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 13.117}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.519646, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 13.117, "width_percent": 4.057}, {"sql": "select * from `customers` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 396}, {"index": 18, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CustomerController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CustomerController.php", "line": 52}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.53247, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "mlk", "explain": null, "start_percent": 17.174, "width_percent": 18.12}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('ApiToken', '4a6e05a546d279b18478eadaa32205a0cf265e5cf80154233abd20bac82df15c', '[\\\"*\\\"]', null, 3, 'Webkul\\\\Customer\\\\Models\\\\Customer', '2025-08-06 09:42:28', '2025-08-06 09:42:28')", "type": "query", "params": [], "bindings": ["ApiToken", "4a6e05a546d279b18478eadaa32205a0cf265e5cf80154233abd20bac82df15c", "[\"*\"]", null, 3, "Webkul\\Customer\\Models\\Customer", "2025-08-06 09:42:28", "2025-08-06 09:42:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, {"index": 19, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CustomerController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CustomerController.php", "line": 78}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.718879, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:64", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=64", "ajax": false, "filename": "HasApiTokens.php", "line": "64"}, "connection": "mlk", "explain": null, "start_percent": 35.294, "width_percent": 29.817}, {"sql": "select * from `customer_groups` where `customer_groups`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/CustomerResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\CustomerResource.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php", "line": 32}], "start": **********.7422018, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:31", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/CustomerResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\CustomerResource.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FCustomerResource.php&line=31", "ajax": false, "filename": "CustomerResource.php", "line": "31"}, "connection": "mlk", "explain": null, "start_percent": 65.112, "width_percent": 15.822}, {"sql": "select * from `customer_profiles` where `customer_profiles`.`customer_id` = 3 and `customer_profiles`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/CustomerResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\CustomerResource.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php", "line": 32}], "start": **********.7470229, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:37", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/CustomerResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\CustomerResource.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FCustomerResource.php&line=37", "ajax": false, "filename": "CustomerResource.php", "line": "37"}, "connection": "mlk", "explain": null, "start_percent": 80.933, "width_percent": 19.067}]}, "models": {"data": {"Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Customer\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/customer/login?locale=en", "action_name": "mlk.api.customers.login", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CustomerController@login", "uri": "POST api/mlk/customer/login", "controller": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CustomerController@login<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCustomerController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/mlk/customer", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCustomerController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CustomerController.php:42-97</a>", "middleware": "api_locale, api_auth", "duration": "567ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-889231100 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889231100\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1579108821 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"8 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579108821\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2103721725 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">281</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">multipart/form-data; boundary=--------------------------455676043700657334765252</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Apifox/1.0.0 (https://apifox.com)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103721725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1199447825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1199447825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-17068305 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 08:42:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17068305\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-339847104 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339847104\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/customer/login?locale=en", "action_name": "mlk.api.customers.login", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CustomerController@login"}, "badge": null}}
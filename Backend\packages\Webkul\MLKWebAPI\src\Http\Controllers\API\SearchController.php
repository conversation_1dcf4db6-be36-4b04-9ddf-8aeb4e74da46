<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Product\Repositories\SearchRepository;
use Webkul\Marketing\Repositories\SearchTermRepository;
use Webkul\Marketing\Jobs\UpdateCreateSearchTerm as UpdateCreateSearchTermJob;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\WishlistRepository;

class SearchController extends APIController
{
    /**
     * BaseProductFormatter instance
     *
     * @var \Webkul\MLKWebAPI\Http\Controllers\API\BaseProductFormatter
     */
    protected $productFormatter;

    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Product\Repositories\ProductRepository  $productRepository
     * @param  \Webkul\Product\Repositories\SearchRepository  $searchRepository
     * @param  \Webkul\Marketing\Repositories\SearchTermRepository  $searchTermRepository
     * @param  \Webkul\Customer\Repositories\CustomerRepository  $customerRepository
     * @param  \Webkul\Customer\Repositories\WishlistRepository  $wishlistRepository
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository,
        protected SearchRepository $searchRepository,
        protected SearchTermRepository $searchTermRepository,
        CustomerRepository $customerRepository,
        WishlistRepository $wishlistRepository
    ) {
        $this->productFormatter = new BaseProductFormatter($customerRepository, $wishlistRepository);
    }

    /**
     * 商品搜索API
     * 
     * 支持以下搜索参数：
     * - query: 搜索关键词
     * - category_id: 分类ID，支持单个分类ID或多个分类ID（逗号分隔），如：1 或 1,2,3
     * - price: 价格范围，格式为 "min,max"
     * - [attribute_code]: 属性值，如 color=red,blue
     * - sort: 排序字段，name-asc，name-desc，created_at-desc，created_at-asc，price-asc，price-asc，price-desc
     * - order: 排序方向 (asc/desc)
     * - page: 页码
     * - limit: 每页数量
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function search()
    {
        $this->validate(request(), [
            'query' => ['sometimes', 'string', 'regex:/^[^\\\\]+$/u'],
            'category_id' => ['sometimes', 'string', 'regex:/^[\d,]+$/'], // 支持多个分类ID，逗号分隔
        ]);

        // 检查是否存在搜索重定向
        $searchTerm = $this->searchTermRepository->findOneWhere([
            'term'       => request()->query('query'),
            'channel_id' => core()->getCurrentChannel()->id,
            'locale'     => app()->getLocale(),
        ]);

        if ($searchTerm?->redirect_url) {
            return $this->success([
                'redirect_url' => $searchTerm->redirect_url
            ]);
        }
        
        // 获取所有可筛选属性
        $filterableAttributes = $this->getFilterableAttributes();
    
        // 将请求参数添加到响应中，用于前端展示当前筛选状态
        $appliedFilters = $this->getAppliedFilters($filterableAttributes);

        // 设置搜索引擎
        if (core()->getConfigData('catalog.products.search.engine') == 'elastic') {
            $searchEngine = core()->getConfigData('catalog.products.search.storefront_mode');
        }
       
        // 获取搜索结果
        $products = $this->productRepository
            ->setSearchEngine($searchEngine ?? 'database')
            ->getAll(array_merge(request()->query(), [
                'channel_id'           => core()->getCurrentChannel()->id,
                'status'               => 1,
                'visible_individually' => 1,
            ]));

        // 记录搜索词
        if (! empty(request()->query('query'))) {
            // 只有当仅有query参数时才更新或创建搜索词
            if (count(request()->except(['mode', 'sort', 'limit'])) == 1) {
                UpdateCreateSearchTermJob::dispatch([
                    'term'       => request()->query('query'),
                    'results'    => $products->total(),
                    'channel_id' => core()->getCurrentChannel()->id,
                    'locale'     => app()->getLocale(),
                ]);
            }
        }

        // 预加载产品价格相关数据以优化查询性能
        $this->productFormatter->preloadProductPriceData($products);

        // 使用BaseProductFormatter格式化产品数据，包含color和discount字段
        $formattedProducts = $this->productFormatter->formatProducts($products);

        //toolbar
        $toolbar = new \Webkul\Product\Helpers\Toolbar();
        return $this->success([
            'total'    => $products->total(),
            'per_page' => $products->perPage(),
            'current_page' => $products->currentPage(),
            'last_page' => $products->lastPage(),
            'products' => $formattedProducts,
            'filterable_attributes' => $filterableAttributes,
            'applied_filters' => $appliedFilters,
            'toolbar' => $toolbar->getAvailableOrders(),
        ]);
    }



    /**
     * 图片搜索接口
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage()
    {
        $this->validate(request(), [
            'image' => 'required|mimes:jpeg,jpg,png,webp|max:2048',
        ]);

        try {
            $imageUrl = $this->searchRepository->uploadSearchImage(request()->all());

            return $this->success([
                'image_url' => $imageUrl
            ]);
        } catch (\Exception $e) {
            return $this->error([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取可筛选属性列表
     *
     * @return array
     */
    protected function getFilterableAttributes()
    {
        $attributeRepository = app(\Webkul\Attribute\Repositories\AttributeRepository::class);
        
        $categoryIds = request()->input('category_id');
        
        if ($categoryIds) {
            $categoryRepository = app(\Webkul\Category\Repositories\CategoryRepository::class);
            
            // 支持多个分类ID，取第一个分类的可筛选属性作为基准
            $categoryIdArray = explode(',', $categoryIds);
            $category = $categoryRepository->find($categoryIdArray[0]);
            
            if ($category && $category->filterableAttributes->count()) {
                $attributes = $category->filterableAttributes;
            } else {
                $attributes = $attributeRepository->getFilterableAttributes();
            }
        } else {
            $attributes = $attributeRepository->getFilterableAttributes();
        }
        
        $attributeOptions = [];
        
        foreach ($attributes as $attribute) {
            if (in_array($attribute->code, ['price', 'visible_individually', 'status'])) {
                continue;
            }
            
            $options = [];
            
            if (in_array($attribute->type, ['select', 'multiselect'])) {
                $options = $attribute->options()->orderBy('sort_order')->get();
                
                $formattedOptions = [];
                foreach ($options as $option) {
                    $formattedOptions[] = [
                        'id' => $option->id,
                        'admin_name' => $option->admin_name,
                        'label' => $option->label,
                        'swatch_value' => $option->swatch_value,
                    ];
                }
                
                $options = $formattedOptions;
            }
            
            $attributeOptions[] = [
                'id' => $attribute->id,
                'code' => $attribute->code,
                'name' => $attribute->name,
                'type' => $attribute->type,
                'options' => $options,
            ];
        }
        
        return $attributeOptions;
    }
    
    /**
     * 获取已应用的过滤条件
     *
     * @param array $filterableAttributes
     * @return array
     */
    protected function getAppliedFilters($filterableAttributes)
    {
        $appliedFilters = [];
        
        foreach ($filterableAttributes as $attribute) {
            $attributeCode = $attribute['code'];
            
            if (request()->has($attributeCode)) {
                $appliedFilters[$attributeCode] = [
                    'attribute' => $attribute,
                    'value' => request()->get($attributeCode),
                ];
            }
        }
        
        // 添加价格过滤
        if (request()->has('price')) {
            $price = explode(',', request()->get('price'));
            
            if (count($price) == 2) {
                $appliedFilters['price'] = [
                    'attribute' => [
                        'code' => 'price',
                        'name' => trans('shop::app.products.price'),
                        'type' => 'price',
                    ],
                    'value' => [
                        'min' => core()->convertPrice($price[0]),
                        'max' => core()->convertPrice($price[1]),
                    ],
                ];
            }
        }
        
        // 添加分类过滤
        if (request()->has('category_id')) {
            $categoryRepository = app(\Webkul\Category\Repositories\CategoryRepository::class);
            $categoryIds = request()->get('category_id');
            $categoryIdArray = explode(',', $categoryIds);
            
            $categories = [];
            foreach ($categoryIdArray as $categoryId) {
                $category = $categoryRepository->find(trim($categoryId));
                if ($category) {
                    $categories[] = [
                        'id' => $category->id,
                        'name' => $category->name,
                        'slug' => $category->slug,
                    ];
                }
            }
            
            if (!empty($categories)) {
                $appliedFilters['category'] = [
                    'attribute' => [
                        'code' => 'category',
                        'name' => 'categories',
                        'type' => 'categories',
                    ],
                    'value' => count($categories) === 1 ? $categories[0] : $categories,
                ];
            }
        }
        
        return $appliedFilters;
    }
} 
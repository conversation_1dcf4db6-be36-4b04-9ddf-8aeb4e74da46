# 购物车API接口文档

## 概述

本文档详细说明了添加商品到购物车的API接口参数和使用方法，包括单个商品添加和批量商品添加功能。

## 接口列表

### 1. 单个商品添加
- **接口路径**: `POST /api/mlk/cart/add`
- **路由名称**: `mlk.api.cart.add`
- **认证要求**: 需要Bearer Token认证 (`auth:sanctum`)
- **Content-Type**: `application/json`

### 2. 批量商品添加
- **接口路径**: `POST /api/mlk/cart/add-batch`
- **路由名称**: `mlk.api.cart.add_batch`
- **认证要求**: 需要Bearer Token认证 (`auth:sanctum`)
- **Content-Type**: `application/json`

## 单个商品添加接口详情

## 基础参数

### 必需参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `product_id` | integer | 是 | 商品ID，必须存在于products表中 | `1` |
| `quantity` | numeric | 否 | 商品数量，默认为1，最小值为1 | `2` |

### 可选参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `is_buy_now` | boolean/string | 否 | 是否立即购买，为true时清空购物车并跳转结账 | `true` |

## 产品类型特定参数

### 1. 可配置商品 (Configurable Products)

可配置商品需要选择具体的变体和属性组合。

#### 必需参数
- `selected_configurable_option` (integer): 选中的子商品ID
- `super_attribute` (object): 属性选择映射

#### 属性ID映射表

根据数据库查询结果，以下是属性ID和选项ID的映射关系：

##### Color 属性 (ID: 23)
```json
{
  "1": "Red",
  "2": "Green", 
  "3": "Yellow",
  "4": "Black",
  "5": "White"
}
```

##### Brand 属性 (ID: 25)
```json
{
  "371": "Apple",
  "372": "Samsung",
  "373": "Huawei", 
  "374": "Xiaomi",
  "375": "OPPO",
  "376": "Google",
  "377": "Motorola",
  "378": "OnePlus",
  "379": "Vivo",
  "380": "Realme"
}
```

##### Device 属性 (ID: 33) - multiselect类型
```json
{
  "381": "iPhone 16 Pro Max",
  "382": "iPhone 16 Pro",
  "383": "iPhone 16",
  "384": "iPhone 16 Plus",
  "400": "Galaxy S25NEW",
  "401": "Galaxy S25+",
  "402": "Galaxy S25",
  "403": "Galaxy XCover 7 Pro",
  "415": "Mate 60 Pro",
  "416": "Mate 60",
  "417": "P60 Pro",
  "425": "Xiaomi 14 Ultra",
  "426": "Xiaomi 14 Pro",
  "427": "Xiaomi 14",
  "435": "Find X7 Ultra",
  "436": "Find X7 Pro",
  "437": "Find X7",
  "445": "Pixel 8 Pro",
  "446": "Pixel 8",
  "447": "Pixel 7 Pro",
  "455": "Edge 50 Pro",
  "456": "Edge 50",
  "457": "Edge 40 Pro",
  "465": "OnePlus 12",
  "466": "OnePlus 11", 
  "467": "OnePlus 10 Pro",
  "475": "X100 Pro",
  "476": "X100",
  "477": "X90 Pro",
  "485": "GT 5 Pro",
  "486": "GT 5",
  "487": "GT Neo 6"
}
```

#### 请求示例

**选择红色Apple iPhone 16 Pro Max:**
```json
{
  "product_id": 1,
  "quantity": 1,
  "selected_configurable_option": 123,
  "super_attribute": {
    "23": "1",    // Color: Red
    "25": "371",  // Brand: Apple
    "33": "381"   // Device: iPhone 16 Pro Max
  }
}
```

**选择黑色Samsung Galaxy S25NEW:**
```json
{
  "product_id": 1,
  "quantity": 2,
  "selected_configurable_option": 124,
  "super_attribute": {
    "23": "4",    // Color: Black
    "25": "372",  // Brand: Samsung
    "33": "400"   // Device: Galaxy S25NEW
  }
}
```

**Device多选示例 (multiselect):**
```json
{
  "product_id": 1,
  "quantity": 1,
  "selected_configurable_option": 125,
  "super_attribute": {
    "23": "5",           // Color: White
    "25": "371",         // Brand: Apple
    "33": "381,382,383"  // Device: 多个设备用逗号分隔
  }
}
```

### 2. 捆绑商品 (Bundle Products)

#### 必需参数
- `bundle_options` (array): 捆绑选项配置

#### 可选参数  
- `bundle_option_qty` (array): 每个捆绑选项的数量

#### 请求示例
```json
{
  "product_id": 1,
  "quantity": 1,
  "bundle_options": {
    "1": ["1"],
    "2": ["3", "4"]
  },
  "bundle_option_qty": {
    "1": 2,
    "2": 1
  }
}
```

### 3. 可下载商品 (Downloadable Products)

#### 必需参数
- `links` (array): 选择的下载链接ID数组

#### 请求示例
```json
{
  "product_id": 1,
  "quantity": 1,
  "links": [1, 2, 3]
}
```

### 4. 分组商品 (Grouped Products)

#### 必需参数
- `qty` (array): 分组中每个商品的数量，格式为 `[product_id => quantity]`

#### 请求示例
```json
{
  "product_id": 1,
  "qty": {
    "10": 2,
    "11": 1,
    "12": 3
  }
}
```

### 5. 自定义选项 (Customizable Options)

#### 可选参数
- `customizable_options` (array): 自定义选项值，格式为 `[option_id => [value1, value2, ...]]`

支持的选项类型：
- `field` - 文本字段
- `area` - 文本区域  
- `file` - 文件上传
- `drop_down` - 下拉选择
- `radio` - 单选按钮
- `checkbox` - 复选框
- `multiple` - 多选
- `date` - 日期选择
- `date_time` - 日期时间选择
- `time` - 时间选择

#### 请求示例
```json
{
  "product_id": 1,
  "quantity": 1,
  "customizable_options": {
    "1": ["自定义文本"],
    "2": ["选项1", "选项2"]
  }
}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "商品已成功添加到购物车",
  "data": {
    "id": 1,
    "customer_id": 1,
    "items_count": 2,
    "items_qty": 3,
    "grand_total": "299.99",
    "items": [
      {
        "id": 1,
        "product_id": 1,
        "quantity": 2,
        "price": "149.99",
        "total": "299.98"
      }
    ]
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息描述",
  "errors": {
    "product_id": ["商品ID是必需的"]
  }
}
```

## 常见错误

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `product_id is required` | 缺少商品ID | 提供有效的商品ID |
| `Product not found` | 商品不存在 | 检查商品ID是否正确 |
| `Product is inactive` | 商品已禁用 | 选择活跃状态的商品 |
| `Missing options` | 缺少必需的选项 | 根据商品类型提供相应参数 |
| `Insufficient quantity` | 库存不足 | 减少购买数量或选择其他商品 |
| `Unauthorized` | 认证失败 | 检查Bearer Token是否有效 |

## 获取子商品ID的SQL查询

要获取可配置商品的`selected_configurable_option`参数值，可以使用以下SQL查询：

```sql
SELECT p.id, p.sku, p.parent_id,
       pav_color.integer_value as color_option_id,
       pav_brand.integer_value as brand_option_id,
       pav_device.text_value as device_option_ids
FROM products p
LEFT JOIN product_attribute_values pav_color ON p.id = pav_color.product_id AND pav_color.attribute_id = 23
LEFT JOIN product_attribute_values pav_brand ON p.id = pav_brand.product_id AND pav_brand.attribute_id = 25  
LEFT JOIN product_attribute_values pav_device ON p.id = pav_device.product_id AND pav_device.attribute_id = 33
WHERE p.parent_id = 父商品ID
ORDER BY p.id;
```

## 注意事项

1. **认证要求**: 所有购物车操作都需要用户认证
2. **属性类型**: Device属性是multiselect类型，支持多个值用逗号分隔
3. **参数验证**: 系统会自动验证商品状态、库存数量等
4. **错误处理**: 建议实现完整的错误处理机制
5. **性能考虑**: 大量商品操作时注意API调用频率限制

## 实际使用示例

### cURL 请求示例

```bash
# 添加可配置商品到购物车
curl -X POST "https://your-domain.com/api/mlk/cart/add" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 1,
    "quantity": 1,
    "selected_configurable_option": 123,
    "super_attribute": {
      "23": "4",
      "25": "371",
      "33": "381"
    }
  }'
```

### JavaScript 请求示例

```javascript
// 使用 fetch API
const addToCart = async (productData) => {
  try {
    const response = await fetch('/api/mlk/cart/add', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(productData)
    });

    const result = await response.json();

    if (result.success) {
      console.log('商品添加成功:', result.data);
    } else {
      console.error('添加失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
};

// 使用示例
addToCart({
  product_id: 1,
  quantity: 2,
  selected_configurable_option: 123,
  super_attribute: {
    "23": "1",    // Red
    "25": "371",  // Apple
    "33": "381"   // iPhone 16 Pro Max
  }
});
```

### PHP 请求示例

```php
<?php
// 使用 Guzzle HTTP 客户端
use GuzzleHttp\Client;

$client = new Client();

$response = $client->post('https://your-domain.com/api/mlk/cart/add', [
    'headers' => [
        'Authorization' => 'Bearer ' . $accessToken,
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
    ],
    'json' => [
        'product_id' => 1,
        'quantity' => 1,
        'selected_configurable_option' => 123,
        'super_attribute' => [
            '23' => '4',    // Black
            '25' => '372',  // Samsung
            '33' => '400'   // Galaxy S25NEW
        ]
    ]
]);

$result = json_decode($response->getBody(), true);

if ($result['success']) {
    echo "商品添加成功\n";
    print_r($result['data']);
} else {
    echo "添加失败: " . $result['message'] . "\n";
}
?>
```

## 最佳实践

### 1. 错误处理
```javascript
const handleCartAdd = async (productData) => {
  try {
    const response = await fetch('/api/mlk/cart/add', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(productData)
    });

    const result = await response.json();

    if (!response.ok) {
      // 处理HTTP错误状态
      throw new Error(result.message || '请求失败');
    }

    if (result.success) {
      // 成功处理
      showSuccessMessage('商品已添加到购物车');
      updateCartUI(result.data);
    } else {
      // 业务逻辑错误
      showErrorMessage(result.message);
    }
  } catch (error) {
    // 网络错误或其他异常
    showErrorMessage('网络错误，请稍后重试');
    console.error('Cart add error:', error);
  }
};
```

### 2. 参数验证
```javascript
const validateCartParams = (params) => {
  const errors = [];

  if (!params.product_id) {
    errors.push('商品ID不能为空');
  }

  if (params.quantity && params.quantity < 1) {
    errors.push('商品数量必须大于0');
  }

  // 可配置商品验证
  if (params.super_attribute && !params.selected_configurable_option) {
    errors.push('请选择商品规格');
  }

  return errors;
};
```

### 3. 用户体验优化
```javascript
// 添加加载状态
const addToCartWithLoading = async (productData, buttonElement) => {
  // 显示加载状态
  buttonElement.disabled = true;
  buttonElement.textContent = '添加中...';

  try {
    const result = await addToCart(productData);

    // 成功反馈
    buttonElement.textContent = '已添加';
    setTimeout(() => {
      buttonElement.textContent = '加入购物车';
      buttonElement.disabled = false;
    }, 2000);

  } catch (error) {
    // 错误处理
    buttonElement.textContent = '添加失败';
    setTimeout(() => {
      buttonElement.textContent = '加入购物车';
      buttonElement.disabled = false;
    }, 2000);
  }
};
```

## 批量商品添加接口详情

### 基础参数

#### 必需参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `products` | array | 是 | 商品数组，包含要添加的所有商品信息 | 见下方示例 |

#### products数组中每个商品的参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `product_id` | integer | 是 | 商品ID，必须存在于products表中 | `1` |
| `quantity` | numeric | 否 | 商品数量，默认为1，最小值为1 | `2` |
| `super_attribute` | object | 否 | 可配置商品的属性选择（与单个添加接口相同） | `{"23": 1, "25": 371}` |
| `booking` | object | 否 | 预订商品的预订信息（与单个添加接口相同） | 见单个添加接口说明 |

### 请求示例

#### 基础批量添加示例
```json
{
    "products": [
        {
            "product_id": 1,
            "quantity": 2
        },
        {
            "product_id": 5,
            "quantity": 1
        }
    ]
}
```

#### 包含可配置商品的批量添加示例
```json
{
    "products": [
        {
            "product_id": 3,
            "quantity": 1,
            "super_attribute": {
                "23": 1,    // 颜色: Red
                "25": 371   // 品牌: Apple
            }
        },
        {
            "product_id": 6,
            "quantity": 2,
            "super_attribute": {
                "23": 2,    // 颜色: Green
                "25": 372   // 品牌: Samsung
            }
        },
        {
            "product_id": 9,
            "quantity": 1
        }
    ]
}
```

### 响应格式

#### 全部成功响应
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "customer_id": 123,
            "items_count": 5,
            "items_qty": 8,
            "grand_total": 899.97,
            "items": [...]
        },
        "success_count": 3,
        "total_count": 3,
        "errors": []
    },
    "message": "3 items added to cart successfully"
}
```

#### 部分成功响应
```json
{
    "success": true,
    "data": {
        "cart": {
            "id": 1,
            "customer_id": 123,
            "items_count": 3,
            "grand_total": 649.98
        },
        "success_count": 2,
        "total_count": 3,
        "errors": [
            {
                "index": 1,
                "product_id": 6,
                "message": "Inactive item cannot be added to cart."
            }
        ]
    },
    "message": "2 items added to cart successfully"
}
```

### 响应数据说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `cart` | object | 更新后的购物车完整信息 |
| `success_count` | integer | 成功添加的商品数量 |
| `total_count` | integer | 请求添加的总商品数量 |
| `errors` | array | 失败商品的错误详情（如果有） |

### 错误处理

批量添加接口采用部分成功处理策略：
- 即使某些商品添加失败，成功的商品仍会被添加到购物车
- 每个失败的商品都会在`errors`数组中详细记录
- `index`字段表示失败商品在请求数组中的索引位置（从0开始）

### 使用场景

1. **愿望清单批量加购**: 用户从愿望清单中选择多个商品一次性添加到购物车
2. **推荐商品批量添加**: 系统推荐的相关商品一键批量加购
3. **套装商品**: 预定义的商品组合批量添加
4. **历史订单重购**: 基于历史订单快速重新购买
5. **促销活动**: 特定活动中的商品批量购买

### 性能优势

- **减少网络请求**: 一次请求完成多个商品添加，提升用户体验
- **原子性操作**: 所有添加操作在同一个购物车会话中完成
- **统一计算**: 批量更新后一次性重新计算购物车总价和优惠
- **错误容错**: 部分失败不影响成功商品的添加

## 故障排除

### 常见问题及解决方案

1. **401 Unauthorized**
   - 检查Bearer Token是否正确
   - 确认Token未过期
   - 验证用户是否已登录

2. **422 Validation Error**
   - 检查必需参数是否完整
   - 验证参数格式是否正确
   - 确认商品ID存在且有效

3. **库存不足错误**
   - 实时检查商品库存
   - 提供库存数量提示
   - 允许用户调整购买数量

4. **商品选项错误**
   - 验证属性ID和选项ID的正确性
   - 确保选择的组合存在对应的子商品
   - 检查multiselect属性的格式

## 更新日志

- 2024-01-13: 初始版本，包含所有商品类型的参数说明
- 2024-01-13: 添加实际使用示例、最佳实践和故障排除指南
- 基于实际数据库查询结果更新属性ID映射表

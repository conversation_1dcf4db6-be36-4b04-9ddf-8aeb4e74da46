<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\CustomerGroupRepository;
use Webkul\Customer\Repositories\CustomerProfileRepository;
use Webkul\MLKWebAPI\Http\Resources\CustomerResource;
use Webkul\Shop\Mail\Customer\EmailVerificationNotification;
use Webkul\Shop\Mail\Customer\RegistrationNotification;
use Webkul\Shop\Mail\ContactUs;

class CustomerController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Customer\Repositories\CustomerRepository  $customerRepository
     * @param  \Webkul\Customer\Repositories\CustomerGroupRepository  $customerGroupRepository
     * @param  \Webkul\Customer\Repositories\CustomerProfileRepository  $customerProfileRepository
     * @return void
     */
    public function __construct(protected CustomerRepository $customerRepository,
                                protected CustomerGroupRepository $customerGroupRepository,
                                protected CustomerProfileRepository $customerProfileRepository)
    {
    }

    /**
     * Login user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email'     => 'required|email',
                'password'  => 'required',
            ]);

            $credentials = request(['email', 'password']);

            if (! Auth::guard('customer')->attempt($credentials)) {
                return $this->error(
                    trans('mlk::app.customers.login-form.invalid-credentials'),
                    401
                );
            }

            $customer = Auth::guard('customer')->user();
            
            // 验证用户是否is_verified
            if (!$customer->is_verified) {
                return $this->error(
                    trans('mlk::app.customers.login-form.verify-first'),
                    401
                );
            }
            
            // 验证用户是否is_suspended
            if ($customer->is_suspended) {
                return $this->error(
                    trans('mlk::app.customers.login-form.not-activated'),
                    401
                );
            }
            
            // 使用Sanctum创建API Token
            $tokenResult = $customer->createToken('ApiToken');
            $token = $tokenResult->plainTextToken;
            
            // 计算token过期时间
            $expirationMinutes = config('sanctum.expiration');
            $expiresAt = null;
            
            if ($expirationMinutes) {
                $expiresAt = now()->addMinutes($expirationMinutes)->toISOString();
            }

            return $this->success([
                'token' => $token,
                'expires_at' => $expiresAt,
                'customer' => new CustomerResource($customer)
            ], trans('mlk::app.customers.login-form.login-success'));
        } catch (\Exception $e) {
            return $this->validationFailed($e->getMessage());
        }
    }

    /**
     * Register user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        try {
            // 基础字段验证
            $validationRules = [
                'first_name'    => 'required',
                'last_name'     => 'required',
                'email'         => 'required|email|unique:customers,email',
                'password'      => 'required|min:6',
                'phone'         => 'required|unique:customers,phone',
                'user_type'     => 'required|in:general,wholesale,guest',
                
                // 通用额外字段
                'social_title'      => 'nullable|string',
                'date_of_birth'     => 'nullable|date',
                'gender'            => 'nullable|string',
                'zip_postal_code'   => 'nullable|string',
                'city'              => 'nullable|string',
                'state'             => 'nullable|string',
                'country'           => 'nullable|string',
            ];
            
            // 批发用户额外字段验证
            if ($request->input('user_type') === 'wholesale') {
                $validationRules = array_merge($validationRules, [
                    'vat_number'            => 'nullable|string',
                    'identification_number' => 'nullable|string',
                    'pec'                   => 'nullable|string',
                    'recipient_code'        => 'nullable|string',
                    'address'               => 'nullable|string',
                    'company'               => 'nullable|string',
                    'invoice_type'          => 'nullable|string',
                    'activity_types'        => 'nullable|array',
                    'how_know_us'           => 'nullable|array',
                ]);
            }
            
            $request->validate($validationRules);

            $data = $request->all();

            // 准备客户基础数据
            $customerData = [
                'first_name'            => $data['first_name'],
                'last_name'             => $data['last_name'],
                'email'                 => $data['email'],
                'password'              => bcrypt($data['password']),
                'phone'                 => $data['phone'],
                'gender'                => $data['gender'] ?? null,
                'date_of_birth'         => $data['date_of_birth'] ?? null,
                'channel_id'            => core()->getCurrentChannel()->id,
                // 邮箱验证相关字段 - 参考Shop模块实现
                'is_verified'           => ! core()->getConfigData('customer.settings.email.verification'),
                'token'                 => md5(uniqid(rand(), true)),
            ];
            
            // 查询customer_groups
            $customerGroup = $this->customerGroupRepository->findOneByField('code', $data['user_type']);
            $customerData['customer_group_id'] = $customerGroup->id;
            
            // 如果user_type == wholesale，则状态设置为is_suspended
            if ($data['user_type'] == 'wholesale') {
                $customerData['is_suspended'] = 1;
                // 批发用户始终需要人工审核，不管邮箱验证配置如何
                $customerData['is_verified'] = 0;
            }
            
            // 创建客户
            $customer = $this->customerRepository->create($customerData);

            // 创建客户额外信息
            $profileData = [
                'customer_id'           => $customer->id,
                'social_title'          => $data['social_title'] ?? null,
                'zip_postal_code'       => $data['zip_postal_code'] ?? null,
                'city'                  => $data['city'] ?? null,
                'state'                 => $data['state'] ?? null,
                'country'               => $data['country'] ?? null,
            ];
            
            // 批发用户额外字段
            if ($data['user_type'] === 'wholesale') {
                $profileData = array_merge($profileData, [
                    'vat_number'            => $data['vat_number'] ?? null,
                    'identification_number' => $data['identification_number'] ?? null,
                    'pec'                   => $data['pec'] ?? null,
                    'recipient_code'        => $data['recipient_code'] ?? null,
                    'address'               => $data['address'] ?? null,
                    'company'               => $data['company'] ?? null,
                    'invoice_type'          => $data['invoice_type'] ?? null,
                    'activity_types'        => $data['activity_types'] ?? [],
                    'how_know_us'           => $data['how_know_us'] ?? [],
                ]);
            }
            
            // 创建客户档案
            $this->customerProfileRepository->createProfile($profileData);

            Event::dispatch('customer.registration.after', $customer);
            
            // 如果user_type == wholesale，则返回等待审核
            if ($data['user_type'] == 'wholesale') {
                return $this->success(
                    [],
                    trans('mlk::app.customers.login-form.not-activated'),
                    201
                );
            }
            
            // 如果启用了邮箱验证且用户未验证，返回需要验证的消息
            if (core()->getConfigData('customer.settings.email.verification') && !$customer->is_verified) {
                return $this->success(
                    [],
                    trans('shop::app.customers.signup-form.success-verify'),
                    201
                );
            }
            
            // 使用Sanctum创建API Token
            $tokenResult = $customer->createToken('ApiToken');
            $token = $tokenResult->plainTextToken;
            
            // 计算token过期时间
            $expirationMinutes = config('sanctum.expiration');
            $expiresAt = null;
            
            if ($expirationMinutes) {
                $expiresAt = now()->addMinutes($expirationMinutes)->toISOString();
            }
            
            return $this->success([
                'token' => $token,
                'expires_at' => $expiresAt,
                'customer' => new CustomerResource($customer)
            ], trans('mlk::app.customers.signup-form.success'), 201);
        } catch (\Exception $e) {
            return $this->validationFailed($e->getMessage());
        }
    }

    
    /**
     * Refresh user token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshToken(Request $request)
    {
        try {
            $customer = auth('sanctum')->user();
            
            if (!$customer) {
                return $this->unauthorized(trans('mlk::app.errors.401.description'));
            }
            
            // 撤销当前token
            auth('sanctum')->user()->currentAccessToken()->delete();
            
            // 创建新的token
            $tokenResult = $customer->createToken('ApiToken');
            $token = $tokenResult->plainTextToken;
            
            // 计算token过期时间
            $expirationMinutes = config('sanctum.expiration');
            $expiresAt = null;
            
            if ($expirationMinutes) {
                $expiresAt = now()->addMinutes($expirationMinutes)->toISOString();
            }
            
            return $this->success([
                'token' => $token,
                'expires_at' => $expiresAt,
                'customer' => new CustomerResource($customer)
            ], 'Token刷新成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * Logout user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            // 撤销当前token
            if (auth('sanctum')->check()) {
                auth('sanctum')->user()->currentAccessToken()->delete();
            }
            
            return $this->success(
                [],
                trans('mlk::app.components.layouts.header.desktop.bottom.logout')
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * Send password reset link to customer email.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function forgotPassword(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
            ]);

            $response = Password::broker('customers')->sendResetLink(
                $request->only(['email'])
            );

            if ($response == Password::RESET_LINK_SENT) {
                return $this->success(
                    [],
                    trans('shop::app.customers.forgot-password.reset-link-sent')
                );
            }

            if ($response == Password::RESET_THROTTLED) {
                return $this->error(
                    trans('shop::app.customers.forgot-password.already-sent'),
                    429
                );
            }

            return $this->error(
                trans('shop::app.customers.forgot-password.email-not-exist'),
                404
            );
        } catch (\Exception $e) {
            return $this->validationFailed($e->getMessage());
        }
    }

    /**
     * Reset customer password (authenticated user).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'email'    => 'required|email',
                'password' => 'required|confirmed|min:6',
            ]);

            // 获取当前认证用户
            $currentUser = auth('sanctum')->user();
            
            if (!$currentUser) {
                return $this->unauthorized(trans('mlk::app.errors.401.description'));
            }

            // 验证邮箱是否与当前用户一致
            if ($currentUser->email !== $request->email) {
                return $this->error(
                    trans('mlk::app.customers.reset-password.email-mismatch'),
                    403
                );
            }

            // 验证当前用户状态
            if (!$currentUser->is_verified) {
                return $this->error(
                    trans('mlk::app.customers.login-form.verify-first'),
                    403
                );
            }

            if ($currentUser->is_suspended) {
                return $this->error(
                    trans('mlk::app.customers.login-form.not-activated'),
                    403
                );
            }

            // 验证新密码不能与当前密码相同
            if (Hash::check($request->password, $currentUser->password)) {
                return $this->error(
                    trans('mlk::app.customers.reset-password.same-password'),
                    400
                );
            }

            // 更新密码
            $this->customerRepository->update([
                'password' => Hash::make($request->password),
            ], $currentUser->id);

            // 重新获取更新后的用户信息
            $updatedCustomer = $this->customerRepository->find($currentUser->id);


            // 触发 Bagisto 自定义事件(不发送邮件，存在响应问题)
           // Event::dispatch('customer.password.update.after', $updatedCustomer);

            // 安全考虑：撤销所有现有的 API tokens，强制重新登录
            $updatedCustomer->tokens()->delete();

            return $this->success(
                [],
                trans('mlk::app.customers.reset-password.success')
            );
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationFailed(
                $e->getMessage(),
                $e->errors()
            );
        } catch (\Exception $e) {
            // 记录错误但不暴露具体信息
            report($e);
            
            return $this->error(
                trans('mlk::app.api.general.error'),
                500
            );
        }
    }

    /**
     * Verify customer account using token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyAccount(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string',
            ]);

            $customer = $this->customerRepository->findOneByField('token', $request->token);

            if (! $customer) {
                return $this->error(
                    trans('shop::app.customers.signup-form.verify-failed'),
                    404
                );
            }

            $this->customerRepository->update([
                'is_verified' => 1,
                'token'       => null,
            ], $customer->id);

            // 发送注册成功邮件
            if ((bool) core()->getConfigData('emails.general.notifications.emails.general.notifications.registration')) {
                Mail::queue(new RegistrationNotification($customer));
            }

            $this->customerRepository->syncNewRegisteredCustomerInformation($customer);

            return $this->success(
                [],
                trans('shop::app.customers.signup-form.verified')
            );
        } catch (\Exception $e) {
            return $this->validationFailed($e->getMessage());
        }
    }

    /**
     * Resend email verification link.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendVerificationEmail(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
            ]);

            $customer = $this->customerRepository->findOneByField('email', $request->email);

            if (! $customer) {
                return $this->error(
                    trans('shop::app.customers.forgot-password.email-not-exist'),
                    404
                );
            }

            if ($customer->is_verified) {
                return $this->error(
                    trans('shop::app.customers.signup-form.already-verified'),
                    400
                );
            }

            $token = md5(uniqid(rand(), true));

            $this->customerRepository->update(['token' => $token], $customer->id);

            // 重新获取更新后的customer对象
            $updatedCustomer = $this->customerRepository->find($customer->id);

            Mail::queue(new EmailVerificationNotification($updatedCustomer));

            return $this->success(
                [],
                trans('shop::app.customers.signup-form.verification-sent')
            );
        } catch (\Exception $e) {
            return $this->validationFailed($e->getMessage());
        }
    }

    /**
     * Send contact us email.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendContactUs(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'name'    => 'string|required',
                'email'   => 'string|required|email',
                'contact' => 'nullable|string',
                'message' => 'required|string',
            ]);

            // 准备邮件数据（与HomeController中的实现保持一致）
            $contactData = $request->only([
                'name',
                'email',
                'contact',
                'message',
            ]);

            // 发送邮件到队列（与原始实现保持一致）
            Mail::queue(new ContactUs($contactData));

            return $this->success(
                [],
                trans('shop::app.home.thanks-for-contact')
            );
        } catch (\Illuminate\Validation\ValidationException $e) {
            // 处理验证错误
            return $this->validationFailed(
                $e->getMessage(),
                $e->errors()
            );
        } catch (\Exception $e) {
            // 记录错误并返回通用错误消息
            report($e);
            
            return $this->error(
                trans('shop::app.common.error'),
                500
            );
        }
    }

    /**
     * Get contact us form translations.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContactUsTranslations()
    {
        try {
            // 获取联系我们表单的所有翻译内容
            $translations = [
                // 页面基本信息
                'title' => trans('shop::app.home.contact.title'),
                'about' => trans('shop::app.home.contact.about'),
                
                // 表单字段标签
                'fields' => [
                    'name' => [
                        'label' => trans('shop::app.home.contact.name'),
                        'placeholder' => trans('shop::app.home.contact.name'),
                        'required' => true,
                    ],
                    'email' => [
                        'label' => trans('shop::app.home.contact.email'),
                        'placeholder' => trans('shop::app.home.contact.email'),
                        'required' => true,
                    ],
                    'contact' => [
                        'label' => trans('shop::app.home.contact.phone-number'),
                        'placeholder' => trans('shop::app.home.contact.phone-number'),
                        'required' => false,
                    ],
                    'message' => [
                        'label' => trans('shop::app.home.contact.desc'),
                        'placeholder' => trans('shop::app.home.contact.describe-here'),
                        'required' => true,
                    ],
                ],
                
                // 按钮和提示信息
                'submit_button' => trans('shop::app.home.contact.submit'),
                'success_message' => trans('shop::app.home.thanks-for-contact'),
                
                // 验证码配置
                'captcha_enabled' => (bool) core()->getConfigData('customer.captcha.credentials.status'),
            ];

            return $this->success(
                $translations
            );
        } catch (\Exception $e) {
            // 记录错误并返回通用错误消息
            report($e);
            
            return $this->error(
                $e->getMessage(),
                500
            );
        }
    }
}
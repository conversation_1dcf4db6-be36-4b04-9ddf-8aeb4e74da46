# MLK购物车API文档说明

## 文档概述

本目录包含了MLK购物车API的完整文档和测试工具，帮助开发者快速理解和使用购物车相关的所有功能。

## 文档结构

### 📚 主要文档

1. **`MLK_CART_API_DOCUMENTATION.md`** - 完整API文档
   - 详细的接口说明
   - 完整的请求/响应示例
   - 错误处理说明
   - 使用示例和注意事项

2. **`MLK_CART_API_QUICK_REFERENCE.md`** - 快速参考手册
   - 简洁的接口列表
   - 快速示例代码
   - 常用参数参考
   - 适合日常开发查阅

3. **`MLK_Cart_API.postman_collection.json`** - Postman测试集合
   - 包含所有API接口的Postman请求
   - 预配置的请求参数
   - 可直接导入Postman使用

4. **`CART_API_COMPARISON.md`** - 功能对比文档
   - MLKWebAPI与Shop包的功能对比
   - 完善过程说明
   - 技术实现细节

## 快速开始

### 1. 查看API概览
首先阅读 `MLK_CART_API_QUICK_REFERENCE.md` 了解所有可用的接口。

### 2. 导入Postman集合
1. 打开Postman
2. 点击 "Import" 按钮
3. 选择 `MLK_Cart_API.postman_collection.json` 文件
4. 配置环境变量：
   - `base_url`: 你的API基础URL (如: `http://localhost:8000/api/mlk`)
   - `auth_token`: 你的Bearer认证令牌

### 3. 测试API
使用Postman集合中的预配置请求测试各个接口功能。

### 4. 集成到项目
参考 `MLK_CART_API_DOCUMENTATION.md` 中的详细说明将API集成到你的项目中。

## API功能概览

### 🛒 购物车基础功能
- 获取购物车内容
- 添加商品到购物车
- 批量添加多个商品到购物车
- 更新商品数量
- 移除商品
- 清空购物车

### 🔧 购物车高级功能
- 批量删除选中商品
- 移动商品到愿望清单
- 应用/移除优惠券
- 估算运费和税费
- 获取交叉销售商品推荐

### 💳 完整结账流程
- 获取结账摘要
- 保存账单和配送地址
- 选择配送方式
- 选择支付方式
- 创建订单

## 认证说明

所有API接口都需要Bearer Token认证：

```bash
Authorization: Bearer your_token_here
```

获取认证令牌请参考用户登录API文档。

## 环境配置

### 开发环境
```
base_url: http://localhost:8000/api/mlk
```

### 生产环境
```
base_url: https://your-domain.com/api/mlk
```

## 常用商品属性参考

在添加可配置商品时，需要提供 `super_attribute` 参数：

```json
{
    "product_id": 456,
    "quantity": 2,
    "super_attribute": {
        "23": 1,   // 颜色属性ID: 值ID (红色)
        "25": 371, // 品牌属性ID: 值ID (Apple)
        "33": 381  // 设备型号ID: 值ID (iPhone 16 Pro Max)
    }
}
```

### 属性值对照表

**颜色 (属性ID: 23)**
- 红色: 1
- 绿色: 2  
- 黄色: 3
- 黑色: 4
- 白色: 5

**品牌 (属性ID: 25)**
- Apple: 371
- Samsung: 372
- Huawei: 373

**设备型号 (属性ID: 33)**
- iPhone 16 Pro Max: 381

## 错误处理

API使用标准HTTP状态码，常见错误：

- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证失败
- `500` - 服务器错误

详细错误信息请查看响应中的 `message` 字段。

## 开发建议

1. **错误处理**: 始终检查API响应的 `success` 字段
2. **数据验证**: 在发送请求前验证必需参数
3. **重试机制**: 对于网络错误实现适当的重试逻辑
4. **缓存策略**: 购物车数据实时性要求高，避免过度缓存
5. **并发控制**: 在高并发场景下注意购物车状态同步

## 支持与反馈

如果在使用过程中遇到问题或有改进建议，请：

1. 检查文档中的常见问题解答
2. 查看错误响应中的详细信息
3. 联系开发团队获取技术支持

## 更新日志

- **v1.0.0** (2024-01-15): 初始版本发布
  - 完整的购物车功能
  - 完整的结账流程
  - 增强的安全验证
  - 完善的文档和测试工具

---

📖 **开始使用**: 建议先阅读快速参考手册，然后使用Postman集合进行测试，最后参考完整文档进行集成开发。

<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Webkul\Core\Repositories\CountryRepository;
use Webkul\Core\Repositories\CountryStateRepository;
use Webkul\Core\Repositories\LocaleRepository;
use Webkul\Category\Repositories\CategoryRepository;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Theme\Repositories\ThemeCustomizationRepository;
use <PERSON><PERSON>an\Location\Facades\Location;

class CoreController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Core\Repositories\CountryRepository  $countryRepository
     * @param  \Webkul\Core\Repositories\CountryStateRepository  $countryStateRepository
     * @param  \Webkul\Core\Repositories\LocaleRepository  $localeRepository
     * @param  \Webkul\Category\Repositories\CategoryRepository  $categoryRepository
     * @param  \Webkul\Attribute\Repositories\AttributeRepository  $attributeRepository
     * @param  \Webkul\Theme\Repositories\ThemeCustomizationRepository  $themeCustomizationRepository
     * @return void
     */
    public function __construct(
        protected CountryRepository $countryRepository,
        protected CountryStateRepository $countryStateRepository,
        protected LocaleRepository $localeRepository,
        protected CategoryRepository $categoryRepository,
        protected AttributeRepository $attributeRepository,
        protected ThemeCustomizationRepository $themeCustomizationRepository
    ) {
    }

    /**
     * Get countries.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCountries()
    {
        return response()->json([
            'data' => core()->countries()->map(fn ($country) => [
                'id'   => $country->id,
                'code' => $country->code,
                'name' => $country->name,
            ]),
        ]);
    }

    /**
     * Get states.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStates()
    {
        return response()->json([
            'data' => core()->groupedStatesByCountries(),
        ]);
    }
    /**
     * Get locales/languages.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLocales()
    {
        // 获取默认渠道
        $channel = core()->getDefaultChannel();
        
        // 获取渠道支持的所有语言
        $locales = $channel->locales;
        
        // 获取渠道默认语言ID
        $defaultLocaleId = $channel->default_locale_id;
        
        // 为每个语言添加是否为默认语言的标记
        $locales->transform(function ($locale) use ($defaultLocaleId) {
            $locale->is_default = $locale->id == $defaultLocaleId;
            return $locale;
        });
        
        // 按名称排序
        $locales = $locales->sortBy('name')->values();

        return response()->json([
            'data' => $locales,
        ]);
    }

    /**
     * 获取所有根分类下的子分类
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        // 获取当前渠道的根分类ID
        $rootCategoryId = core()->getCurrentChannel()->root_category_id;
        
        // 获取根分类下的所有子分类树
        $categoryTree = $this->categoryRepository->getVisibleCategoryTree($rootCategoryId);
        
        // 转换分类为统一格式
        $formattedCategories = $this->formatCategories($categoryTree);
        return response()->json([
            'data' => $formattedCategories,
        ]);
    }

    public function getBrands()
    {
        // 获取请求参数
        $targetBrands = request()->post('brands', []);
       
        // 获取当前语言代码
        $currentLocale = app()->getLocale();
        
        // 查找brand属性
        $brandAttribute = $this->attributeRepository->findOneByField('code', 'brand');
        
        // 查找Device属性
        $deviceAttribute = $this->attributeRepository->findOneByField('code', 'device');
        
        if (!$brandAttribute) {
            return $this->success(['brands' => []]);
        }
        
        // 获取品牌选项的查询构建器
        $brandQuery = $brandAttribute->options()->with(['translations'])->orderBy('sort_order');
        
        // 如果指定了目标品牌，则过滤品牌
        if (!empty($targetBrands)) {
            $targetBrands = json_decode($targetBrands, true);
            $brandQuery->whereHas('translations', function ($query) use ($targetBrands) {
                $query->where(function ($subQuery) use ($targetBrands) {
                    foreach ($targetBrands as $brand) {
                        $subQuery->orWhere('label', 'like', '%' . $brand . '%');
                    }
                });
            })->orWhere(function ($query) use ($targetBrands) {
                foreach ($targetBrands as $brand) {
                    $query->orWhere('admin_name', 'like', '%' . $brand . '%');
                }
            });
        }
        
        $brandOptions = $brandQuery->get();
        
        // 获取设备选项（如果存在device属性）
        $deviceOptions = collect();
        if ($deviceAttribute) {
            $deviceOptions = $deviceAttribute->options()->with(['translations'])->orderBy('sort_order')->get();
        }
        
        // 构建品牌数据并关联对应的设备
        $brands = $brandOptions->map(function ($brandOption) use ($currentLocale, $deviceOptions) {
            // 查找属于这个品牌的设备
            $relatedDevices = $deviceOptions->filter(function ($deviceOption) use ($brandOption) {
                return $deviceOption->parent_id == $brandOption->id;
            });
            
            $relatedDevices = $relatedDevices->map(function ($deviceOption) use ($currentLocale) {
                return [
                    'id' => $deviceOption->id,
                    'label' => $deviceOption->translate($currentLocale)?->label ?? $deviceOption->admin_name,
                    'sort_order' => $deviceOption->sort_order,
                    // 'swatch_value' => $deviceOption->swatch_value,
                    // 'swatch_value_url' => Storage::url($deviceOption->swatch_value),
                ];
            })->values();
            
            return [
                'id' => $brandOption->id,
                'label' => $brandOption->translate($currentLocale)?->label ?? $brandOption->admin_name,
                'sort_order' => $brandOption->sort_order,
                'swatch_value' => $brandOption->swatch_value,
                'swatch_value_url' => Storage::url($brandOption->swatch_value),
                'devices' => $relatedDevices,
            ];
        });
        
        return $this->success(['brands' => $brands]);
    }

    /**
     * 将分类转换为统一格式
     *
     * @param array $categories
     * @param int $position
     * @param bool $isRoot
     * @return array
     */
    protected function formatCategories($categories, $startPosition = 1)
    {
        $formattedCategories = [];
        
        $position = $startPosition;
        
        foreach ($categories as $category) {
            $formattedCategory = [
                'name' => $category->name,
                'slug' => $category->slug,
                'position' => $position++,
                'logo_path' => $category->logo_url ?? '',
                'category_ids' => [$category->id],
                'children' => [],
            ];
            
            // 递归处理子分类
            if (!empty($category->children)) {
                $formattedCategory['children'] = $this->formatCategories($category->children, 1);
            }
            
            $formattedCategories[] = $formattedCategory;
        }
        
        return $formattedCategories;
    }

    /**
     * 获取品牌初始化数据
     *
     * @return array
     */
    protected function getBrandInitialData()
    {
        // 获取当前语言代码
        $currentLocale = app()->getLocale();
        
        // 只查询必要的属性ID，避免加载所有属性数据
        $attributeIds = DB::table('attributes')
            ->whereIn('code', ['brand', 'device'])
            ->pluck('id', 'code');
        
        $brandAttributeId = $attributeIds->get('brand');
        $deviceAttributeId = $attributeIds->get('device');
        
        if (!$brandAttributeId) {
            return [];
        }
        
        // 只查询brand和device属性相关的选项，大大减少数据量
        $attributeOptionsQuery = DB::table('attribute_options')
            ->whereIn('attribute_id', array_filter([$brandAttributeId, $deviceAttributeId]))
            ->orderBy('sort_order');
        
        // 获取选项基础数据
        $attributeOptions = $attributeOptionsQuery->get();
        $optionIds = $attributeOptions->pluck('id');
        
        // 只查询这些选项的翻译数据
        $translations = DB::table('attribute_option_translations')
            ->whereIn('attribute_option_id', $optionIds)
            ->whereIn('locale', [$currentLocale, 'en'])
            ->get()
            ->groupBy('attribute_option_id');
        
        // 分离品牌和设备选项
        $brandOptions = $attributeOptions->where('attribute_id', $brandAttributeId);
        $deviceOptions = $attributeOptions->where('attribute_id', $deviceAttributeId);
        
        // 预处理品牌数据
        $processedBrands = $brandOptions->map(function ($option) use ($translations, $currentLocale) {
            $optionTranslations = $translations->get($option->id, collect());
            $translation = $optionTranslations->where('locale', $currentLocale)->first() 
                        ?? $optionTranslations->where('locale', 'en')->first();
            
            return [
                'id' => $option->id,
                'name' => $translation ? $translation->label : $option->admin_name,
                'admin_name' => $option->admin_name,
                'sort_order' => $option->sort_order,
                'swatch_value' => $option->swatch_value,
            ];
        })->keyBy('id');
        
        // 预处理设备数据，按品牌ID分组
        $devicesByBrand = $deviceOptions->groupBy('parent_id')->map(function ($devices) use ($translations, $currentLocale) {
            return $devices->map(function ($option) use ($translations, $currentLocale) {
                $optionTranslations = $translations->get($option->id, collect());
                $translation = $optionTranslations->where('locale', $currentLocale)->first() 
                            ?? $optionTranslations->where('locale', 'en')->first();
                
                return [
                    'id' => $option->id,
                    'name' => $translation ? $translation->label : $option->admin_name,
                    'sort_order' => $option->sort_order,
                ];
            })->sortBy('sort_order')->values();
        });
        
        // 查找Apple和Samsung品牌
        $appleBrand = $processedBrands->first(function ($brand) {
            return strtolower($brand['name']) === 'apple';
        });
        
        $samsungBrand = $processedBrands->first(function ($brand) {
            return strtolower($brand['name']) === 'samsung';
        });
        
        $brandData = [];
        
        // 添加Apple品牌数据
        if ($appleBrand) {
            $appleDevices = $devicesByBrand->get($appleBrand['id'], collect())->map(function ($device) {
                return [
                    'name' => $device['name'],
                    'slug' => '',
                    'position' => $device['sort_order'],
                    'logo_path' => '',
                    'category_ids' => [$device['id']],
                    'query' => 'device='.$device['id'],
                    'children' => [],
                ];
            })->toArray();
            
            $brandData[] = [
                'name' => $appleBrand['name'],
                'slug' => '',
                'position' => 1,
                'logo_path' => $appleBrand['swatch_value'] ? Storage::url($appleBrand['swatch_value']) : '',
                'category_ids' => [$appleBrand['id']],
                'query' => 'brand='.$appleBrand['id'],
                'children' => $appleDevices,
            ];
        }
        
        // 添加Samsung品牌数据  
        if ($samsungBrand) {
            $samsungDevices = $devicesByBrand->get($samsungBrand['id'], collect())->map(function ($device) {
                return [
                    'name' => $device['name'],
                    'slug' => '',
                    'position' => $device['sort_order'],
                    'logo_path' => '',
                    'category_ids' => [$device['id']],
                    'query' => 'device='.$device['id'],
                    'children' => [],
                ];
            })->toArray();
            
            $brandData[] = [
                'name' => $samsungBrand['name'],
                'slug' => '',
                'position' => 2,
                'logo_path' => $samsungBrand['swatch_value'] ? Storage::url($samsungBrand['swatch_value']) : '',
                'category_ids' => [$samsungBrand['id']],
                'query' => 'brand='.$samsungBrand['id'],
                'children' => $samsungDevices,
            ];
        }
        
        // 获取其他品牌（排除Apple和Samsung）
        $excludeIds = array_filter([
            $appleBrand ? $appleBrand['id'] : null,
            $samsungBrand ? $samsungBrand['id'] : null
        ]);
        
        $otherBrands = $processedBrands->reject(function ($brand) use ($excludeIds) {
            return in_array($brand['id'], $excludeIds);
        })->map(function ($brand) {
            return [
                'name' => $brand['name'],
                'slug' => '',
                'position' => $brand['sort_order'],
                'logo_path' => $brand['swatch_value'] ? Storage::url($brand['swatch_value']) : '',
                'category_ids' => [$brand['id']],
                'query' => 'brand='.$brand['id'],
                'children' => [],
            ];
        })->values()->toArray();
        
        // 添加More品牌数据
        $brandData[] = [
            'name' => trans('mlk::app.api.navbar.more-devices'),
            'slug' => '',
            'position' => 3,
            'logo_path' => '',
            'category_ids' => [],
            'query' => '',
            'children' => $otherBrands,
        ];
        
        return $brandData;
    }

    /**
     * 获取导航栏
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNavbar()
    {
        // 获取当前语言环境
        $locale = app()->getLocale();
        
        // 获取当前渠道的根分类ID
        $rootCategoryId = core()->getCurrentChannel()->root_category_id;
        
        // // 获取根分类下的所有子分类树
        $categoryTree = $this->categoryRepository->getVisibleCategoryTree($rootCategoryId);
        
        // // 转换分类为统一格式
        $devices = [
            'name' => trans('mlk::app.api.navbar.devices'),
            'slug' => '',
            'position' => 0,
            'logo_path' => '',
            'category_ids' => [],
            'children' => $this->getBrandInitialData(),
        ];
        $formattedCategories = array_merge([$devices], $this->formatCategories($categoryTree));
       
        return $this->success($formattedCategories);
    }

    /**
     * 获取header_offer配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHeaderOffer()
    {
        // 获取当前渠道代码
        $currentChannelCode = core()->getCurrentChannel()->code;
        
        // 获取当前语言代码
        $currentLocaleCode = app()->getLocale();
        
        // 获取header_offer相关的三个配置值
        $title = core()->getConfigData('general.content.header_offer.title', $currentChannelCode, $currentLocaleCode) ?? '';
        $redirectionTitle = core()->getConfigData('general.content.header_offer.redirection_title', $currentChannelCode, $currentLocaleCode) ?? '';
        $redirectionLink = core()->getConfigData('general.content.header_offer.redirection_link', $currentChannelCode, $currentLocaleCode) ?? '';
        
        return $this->success([
            'title' => $title,
            'redirection_title' => $redirectionTitle,
            'redirection_link' => $redirectionLink,
        ]);
    }

    /**
     * 获取底部页面内容
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFooter()
    {
        // 获取当前渠道代码
        $currentChannelCode = core()->getCurrentChannel()->code;
        
        // 获取当前语言代码
        $currentLocaleCode = app()->getLocale();

        // 检查用户是否为批发客户
        $isWholesale = false;
        $token = request()->bearerToken();
        if ($token) {
            $accessToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if ($accessToken && $accessToken->tokenable) {
                $user = $accessToken->tokenable;
                if (isset($user->customer_group_id)) {
                    $customerGroup = app(\Webkul\Customer\Repositories\CustomerGroupRepository::class)->find($user->customer_group_id);
                    if ($customerGroup && $customerGroup->code === 'wholesale') {
                        $isWholesale = true;
                    }
                }
            }
        }

        // 获取footer_links
        $footer_links = $this->getThemeCustomization('footer_links', $currentLocaleCode, $isWholesale);
        
        return $this->success([
            'footer_links' => $footer_links,
        ]);
    }

    /**
     * 获取主题自定义
     *
     * @param string $name
     * @param string $locale
     * @param bool $isWholesale
     * @return array
     */
    protected function getThemeCustomization($name, $locale, $isWholesale)
    {
        // 根据是否批发客户确定查询的主题名称
        $themeName = $isWholesale ? 'wholesale_'.$name : $name;
        
        // 获取当前渠道ID
        $channelId = core()->getCurrentChannel()->id;

        // 查询主题自定义表
        $themeCustomization = $this->themeCustomizationRepository->findWhere([
            'name' => $themeName,
            'channel_id' => $channelId,
            'status' => 1
        ])->first();
      
        if (!$themeCustomization) {
            return [];
        }
       
        // 获取当前语言的翻译
        $translation = $themeCustomization->translate($locale);
        // 如果当前语言没有翻译，则使用en翻译
        if (!$translation) {
            $translation = $themeCustomization->translate('en');
        }        
        return $translation ? $translation->options : [];
    }



    public function checkIp()
    {
        $europeanCountryCodes = [
            'AL', 'AD', 'AT', 'BY', 'BE', 'BA', 'BG', 'HR', 'CY', 'CZ',
            'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IS', 'IE', 'IT',
            'XK', // 科索沃
            'LV', 'LI', 'LT', 'LU', 'MT', 'MD', 'MC', 'ME', 'NL', 'MK',
            'NO', 'PL', 'PT', 'RO', 'RU', // 俄罗斯
            'SM', 'RS', 'SK', 'SI', 'ES', 'SE', 'CH', 'TR', // 土耳其
            'UA', 'GB', 'VA',
        ];
        $ip = request()->ip();
        $location = Location::get($ip);

        $isEuropean = false;
        $countryName = 'Unknown';
        $countryCode = 'Unknown';

        if ($location && $location->countryCode) {
            $countryCode = strtoupper($location->countryCode);
            $countryName = $location->countryName;
            $isEuropean = in_array($countryCode, $europeanCountryCodes);
        }
        return $this->success([
            'is_european' => $isEuropean,
            'country_name' => $countryName,
            'country_code' => $countryCode,
            'ip' => $ip,
        ]);
    }
} 
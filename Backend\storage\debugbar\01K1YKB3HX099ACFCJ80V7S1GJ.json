{"__meta": {"id": "01K1YKB3HX099ACFCJ80V7S1GJ", "datetime": "2025-08-06 03:28:38", "utime": **********.590257, "method": "GET", "uri": "/cache/large/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.368324, "end": **********.599462, "duration": 0.23113799095153809, "duration_str": "231ms", "measures": [{"label": "Booting", "start": **********.368324, "relative_start": 0, "end": **********.569803, "relative_end": **********.569803, "duration": 0.****************, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.569813, "relative_start": 0.*****************, "end": **********.599464, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "29.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.580999, "relative_start": 0.*****************, "end": **********.584428, "relative_end": **********.584428, "duration": 0.0034291744232177734, "duration_str": "3.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.588736, "relative_start": 0.***************, "end": **********.588855, "relative_end": **********.588855, "duration": 0.00011897087097167969, "duration_str": "119μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.588868, "relative_start": 0.*****************, "end": **********.588879, "relative_end": **********.588879, "duration": 1.1205673217773438e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "231ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1162096284 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1162096284\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-999611598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-999611598\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1041113590 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImhVVzROUms5SDVUaGEyL0RyNlZ5bkE9PSIsInZhbHVlIjoiN0RGL3FYT3EvTFFYNi9mVHYyb0hkOVJPSE9GYlQwMWtNNzUvS25RdTFneTdJeStHN0RRTmwyNDlHU2J2dWJUdTRMSnZkUHRyeVdaMFZMYkxnYTdzSTdiclZJVkQ0SkRibVFtck43emRVRXpDUG1tbVdMSlI2bjd3bWlSVFNrdXoiLCJtYWMiOiIxZGViMGU4MzFhYWVlNzZiNDI4Y2ZkZDkzNGI1ZmRiMzM2OTljYmEzYzY3Mzg0ZTVjNmI2ZTNiMzk0YzgwZTMyIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Inl5UFdjdzl5dVZhRmVQUjRjL2gvbXc9PSIsInZhbHVlIjoiSVptaGFKUGlEYmpIZSt2SFVSZHljZW5RMklXNnQzNkRqMnpVMWdsQ2dHVUsvc2tCQkRoNk1oZXpaK2YydFIwYjVDdjdiRWhPT2dsang3ZHNzSTc2T0g5bjRPZlhxRzYzZkVyZXloZ2xuRU5kUGpPekExYzhBbElOUEl3Z28vMVMiLCJtYWMiOiJjMDU3NGRjYzU1NTcyMDZkMDZmYmJlYmQ2YjkzZWMyOGY5ZTI4ODg1NzkwMmMyOTRlYTNmYjhmNDIyNDc2YWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://mlk.test/SKU-CS-011-variant-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041113590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhVVzROUms5SDVUaGEyL0RyNlZ5bkE9PSIsInZhbHVlIjoiN0RGL3FYT3EvTFFYNi9mVHYyb0hkOVJPSE9GYlQwMWtNNzUvS25RdTFneTdJeStHN0RRTmwyNDlHU2J2dWJUdTRMSnZkUHRyeVdaMFZMYkxnYTdzSTdiclZJVkQ0SkRibVFtck43emRVRXpDUG1tbVdMSlI2bjd3bWlSVFNrdXoiLCJtYWMiOiIxZGViMGU4MzFhYWVlNzZiNDI4Y2ZkZDkzNGI1ZmRiMzM2OTljYmEzYzY3Mzg0ZTVjNmI2ZTNiMzk0YzgwZTMyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Inl5UFdjdzl5dVZhRmVQUjRjL2gvbXc9PSIsInZhbHVlIjoiSVptaGFKUGlEYmpIZSt2SFVSZHljZW5RMklXNnQzNkRqMnpVMWdsQ2dHVUsvc2tCQkRoNk1oZXpaK2YydFIwYjVDdjdiRWhPT2dsang3ZHNzSTc2T0g5bjRPZlhxRzYzZkVyZXloZ2xuRU5kUGpPekExYzhBbElOUEl3Z28vMVMiLCJtYWMiOiJjMDU3NGRjYzU1NTcyMDZkMDZmYmJlYmQ2YjkzZWMyOGY5ZTI4ODg1NzkwMmMyOTRlYTNmYjhmNDIyNDc2YWJjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">9870</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">52106b6549105ff727c316ef25a13f12</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 02:28:38 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1084225013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1084225013\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}